#!/usr/bin/env python3
"""Test simple CSV"""
import csv
import os

csv_path = "data/rna_waldec_20250701/rna_waldec_20250701_dpt_975.csv"
print(f"Fichier existe: {os.path.exists(csv_path)}")

if os.path.exists(csv_path):
    with open(csv_path, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f, delimiter=';')
        print(f"Colonnes: {reader.fieldnames}")
        
        for i, row in enumerate(reader):
            if i >= 3:
                break
            rna = row.get('id', '').strip().strip('"')
            print(f"RNA {i+1}: {rna}")
        
    print("✅ Test terminé")
else:
    print("❌ Fichier non trouvé")