#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 SCRAPER COMPLET - VERSION FINALE FONCTIONNELLE
Version consolidée avec vrai scraping Selenium intégré
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import os
import sys
import threading
import time
import csv
import json
from datetime import datetime
import logging
import pandas as pd
from bs4 import BeautifulSoup

# Imports Selenium
try:
    from selenium import webdriver
    from selenium.webdriver.chrome.service import Service as ChromeService
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.common.exceptions import TimeoutException, WebDriverException
except ImportError:
    print("⚠️ Selenium non disponible - mode simulation")

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('scraper.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ScraperEngine:
    """Moteur de scraping avec Selenium"""
    
    def __init__(self, driver_path="./chromedriver.exe"):
        self.driver_path = driver_path
        self.driver = None
        self.base_url = "https://www.data-asso.fr/annuaire/association"
        self.is_initialized = False
        
    def init_driver(self):
        """Initialise le WebDriver"""
        try:
            options = webdriver.ChromeOptions()
            options.add_argument("--headless")
            options.add_argument("--no-sandbox")
            options.add_argument("--disable-dev-shm-usage")
            options.add_argument("--disable-gpu")
            options.add_argument("--disable-extensions")
            options.add_argument("--disable-images")
            options.add_argument("--window-size=1920,1080")
            
            # Préférences pour la performance
            prefs = {
                "profile.managed_default_content_settings.images": 2,
                "profile.default_content_setting_values.notifications": 2,
            }
            options.add_experimental_option("prefs", prefs)
            
            service = ChromeService(executable_path=self.driver_path)
            self.driver = webdriver.Chrome(service=service, options=options)
            self.is_initialized = True
            logger.info("✅ WebDriver initialisé")
            return True
            
        except Exception as e:
            logger.error(f"❌ Erreur init WebDriver: {e}")
            return False
    
    def scrape_association(self, rna):
        """Scrape une association depuis son RNA"""
        if not self.is_initialized:
            if not self.init_driver():
                return None
                
        url = f"{self.base_url}/{rna}"
        
        try:
            self.driver.get(url)
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            soup = BeautifulSoup(self.driver.page_source, 'html.parser')
            
            # Extraction des données
            result = {
                'rna': rna,
                'nom': self._extract_name(soup),
                'adresse': self._extract_address(soup),
                'telephone': self._extract_contact(soup, 'Téléphone'),
                'email': self._extract_contact(soup, 'E-mail'),
                'site_web': self._extract_contact(soup, 'Site web'),
                'objet': self._extract_object(soup),
                'date_creation': self._extract_date(soup),
                'statut': self._extract_status(soup)
            }
            
            return result
            
        except Exception as e:
            logger.error(f"❌ Erreur scraping {rna}: {e}")
            return None
    
    def _extract_name(self, soup):
        """Extraction du nom"""
        try:
            element = soup.find('p', {'data-test-title': 'association'})
            return element.get_text(strip=True) if element else ""
        except:
            return ""
    
    def _extract_address(self, soup):
        """Extraction de l'adresse"""
        try:
            coordonnees = soup.find('div', {'id': 'coordonnees'})
            if coordonnees:
                address_h5 = coordonnees.find('h5', string=lambda x: x and 'Adresse' in x and 'gestion' not in x)
                if address_h5:
                    address_div = address_h5.find_next_sibling('div')
                    if address_div:
                        return address_div.get_text(separator=', ', strip=True)
        except:
            pass
        return ""
    
    def _extract_contact(self, soup, contact_type):
        """Extraction des contacts"""
        try:
            coordonnees = soup.find('div', {'id': 'coordonnees'})
            if coordonnees:
                divs = coordonnees.find_all('div', class_='column')
                for i, div in enumerate(divs):
                    if contact_type in div.get_text():
                        if i + 1 < len(divs):
                            value = divs[i + 1].get_text(strip=True)
                            return value if value != '-' else ""
        except:
            pass
        return ""
    
    def _extract_object(self, soup):
        """Extraction de l'objet"""
        try:
            activites = soup.find('div', {'id': 'activites'})
            if activites:
                object_h5 = activites.find('h5', string=lambda x: x and 'Objet' in x)
                if object_h5:
                    object_p = object_h5.find_next_sibling('p')
                    if object_p:
                        return object_p.get_text(strip=True)
        except:
            pass
        return ""
    
    def _extract_date(self, soup):
        """Extraction de la date de création"""
        try:
            identite = soup.find('div', {'id': 'identite'})
            if identite:
                date_h5 = identite.find('h5', string=lambda x: x and 'Date de création' in x)
                if date_h5:
                    date_p = date_h5.find_next_sibling('p')
                    if date_p:
                        return date_p.get_text(strip=True)
        except:
            pass
        return ""
    
    def _extract_status(self, soup):
        """Extraction du statut"""
        try:
            identite = soup.find('div', {'id': 'identite'})
            if identite:
                tag = identite.find('span', class_='tag')
                if tag:
                    return tag.get_text(strip=True)
        except:
            pass
        return ""
    
    def close(self):
        """Ferme le driver"""
        if self.driver:
            self.driver.quit()
            logger.info("🔒 WebDriver fermé")

class ScraperAppComplete:
    """Application complète avec interface graphique"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🔧 Scraper d'Associations - VERSION COMPLÈTE")
        self.root.geometry("1000x800")
        self.root.configure(bg='#1e1e1e')
        
        # Variables
        self.is_running = False
        self.stop_flag = False
        self.scraper_engine = None
        self.current_thread = None
        
        # Configuration
        self.chromedriver_path = "./chromedriver.exe"
        self.data_dir = "data/rna_waldec_20250701"
        self.output_dir = "output"
        
        os.makedirs(self.output_dir, exist_ok=True)
        
        self.setup_ui()
        self.verify_system()
    
    def setup_ui(self):
        """Configuration de l'interface"""
        # Style
        style = ttk.Style()
        style.theme_use('clam')
        
        # Header
        header = tk.Frame(self.root, bg='#2d5aa0', height=80)
        header.pack(fill='x')
        header.pack_propagate(False)
        
        title = tk.Label(header, 
                        text="🔧 SCRAPER D'ASSOCIATIONS - VERSION COMPLÈTE FONCTIONNELLE", 
                        font=('Arial', 14, 'bold'),
                        fg='white', bg='#2d5aa0')
        title.pack(expand=True)
        
        # Main container
        main_container = tk.Frame(self.root, bg='#1e1e1e')
        main_container.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Left panel - Controls
        left_panel = tk.Frame(main_container, bg='#2a2a2a', width=350)
        left_panel.pack(side='left', fill='y', padx=(0, 10))
        left_panel.pack_propagate(False)
        
        # Configuration section
        config_frame = tk.LabelFrame(left_panel, text="⚙️ CONFIGURATION", 
                                    fg='white', bg='#2a2a2a', font=('Arial', 10, 'bold'))
        config_frame.pack(fill='x', padx=10, pady=10)
        
        # Mode de fonctionnement
        tk.Label(config_frame, text="Mode:", fg='white', bg='#2a2a2a').pack(anchor='w')
        self.mode_var = tk.StringVar(value="test")
        mode_frame = tk.Frame(config_frame, bg='#2a2a2a')
        mode_frame.pack(fill='x', pady=2)
        
        tk.Radiobutton(mode_frame, text="Test (simulation)", variable=self.mode_var, 
                      value="test", fg='white', bg='#2a2a2a', selectcolor='#444').pack(anchor='w')
        tk.Radiobutton(mode_frame, text="Réel (Selenium)", variable=self.mode_var, 
                      value="real", fg='white', bg='#2a2a2a', selectcolor='#444').pack(anchor='w')
        
        # Limite
        tk.Label(config_frame, text="Nombre d'associations:", fg='white', bg='#2a2a2a').pack(anchor='w', pady=(10,0))
        self.limit_var = tk.StringVar(value="3")
        tk.Entry(config_frame, textvariable=self.limit_var, width=10).pack(anchor='w')
        
        # Département
        tk.Label(config_frame, text="Département:", fg='white', bg='#2a2a2a').pack(anchor='w', pady=(10,0))
        self.dept_var = tk.StringVar(value="rna_waldec_20250701_dpt_975.csv")
        dept_combo = ttk.Combobox(config_frame, textvariable=self.dept_var, width=40)
        dept_combo.pack(pady=2)
        
        # Charger départements
        try:
            files = [f for f in os.listdir(self.data_dir) if f.endswith('.csv')]
            dept_combo['values'] = sorted(files)
        except:
            dept_combo['values'] = []
        
        # Actions
        actions_frame = tk.LabelFrame(left_panel, text="🚀 ACTIONS", 
                                     fg='white', bg='#2a2a2a', font=('Arial', 10, 'bold'))
        actions_frame.pack(fill='x', padx=10, pady=10)
        
        self.start_btn = tk.Button(actions_frame, text="▶️ DÉMARRER SCRAPING", 
                                  command=self.start_scraping,
                                  bg='#4CAF50', fg='white', font=('Arial', 11, 'bold'),
                                  height=2)
        self.start_btn.pack(fill='x', pady=5)
        
        self.stop_btn = tk.Button(actions_frame, text="⏹️ ARRÊTER", 
                                 command=self.stop_scraping,
                                 bg='#f44336', fg='white', font=('Arial', 11, 'bold'),
                                 state='disabled')
        self.stop_btn.pack(fill='x', pady=2)
        
        tk.Button(actions_frame, text="📁 OUVRIR RÉSULTATS", 
                 command=self.open_results,
                 bg='#2196F3', fg='white', font=('Arial', 10)).pack(fill='x', pady=2)
        
        tk.Button(actions_frame, text="🗑️ NETTOYER LOGS", 
                 command=self.clear_logs,
                 bg='#FF5722', fg='white', font=('Arial', 10)).pack(fill='x', pady=2)
        
        # Status
        status_frame = tk.LabelFrame(left_panel, text="📊 STATUS", 
                                    fg='white', bg='#2a2a2a', font=('Arial', 10, 'bold'))
        status_frame.pack(fill='x', padx=10, pady=10)
        
        self.status_label = tk.Label(status_frame, text="✅ Prêt", 
                                    fg='#4CAF50', bg='#2a2a2a', font=('Arial', 10, 'bold'))
        self.status_label.pack(pady=5)
        
        self.progress = ttk.Progressbar(status_frame, mode='determinate')
        self.progress.pack(fill='x', pady=5)
        
        self.progress_label = tk.Label(status_frame, text="0/0", 
                                      fg='white', bg='#2a2a2a')
        self.progress_label.pack()
        
        # Right panel - Logs
        right_panel = tk.Frame(main_container, bg='#1e1e1e')
        right_panel.pack(side='right', fill='both', expand=True)
        
        logs_label = tk.Label(right_panel, text="📋 LOGS EN TEMPS RÉEL", 
                             font=('Arial', 12, 'bold'),
                             fg='white', bg='#1e1e1e')
        logs_label.pack(pady=5)
        
        # Zone de logs avec scrollbar
        self.logs_text = scrolledtext.ScrolledText(right_panel, 
                                                  bg='#000000', fg='#00ff00',
                                                  font=('Consolas', 9),
                                                  wrap=tk.WORD)
        self.logs_text.pack(fill='both', expand=True, padx=5, pady=5)
        
        # Bottom status bar
        bottom_frame = tk.Frame(self.root, bg='#333333', height=30)
        bottom_frame.pack(fill='x')
        bottom_frame.pack_propagate(False)
        
        self.bottom_status = tk.Label(bottom_frame, 
                                     text="🚀 Application initialisée - Prête à fonctionner", 
                                     fg='white', bg='#333333')
        self.bottom_status.pack(side='left', padx=10, pady=5)
        
    def verify_system(self):
        """Vérifie que tout est configuré correctement"""
        self.log("🔍 === VÉRIFICATION DU SYSTÈME ===")
        
        # ChromeDriver
        if os.path.exists(self.chromedriver_path):
            self.log("✅ ChromeDriver trouvé")
        else:
            self.log("❌ ChromeDriver non trouvé!")
            
        # Dossier data
        if os.path.exists(self.data_dir):
            files = [f for f in os.listdir(self.data_dir) if f.endswith('.csv')]
            self.log(f"✅ Dossier data trouvé ({len(files)} fichiers CSV)")
        else:
            self.log("❌ Dossier data non trouvé!")
            
        # Dépendances
        try:
            import selenium, pandas, bs4
            self.log("✅ Toutes les dépendances sont disponibles")
        except ImportError as e:
            self.log(f"⚠️ Dépendance manquante: {e}")
            
        self.log("🚀 Système vérifié - Prêt pour le scraping!")
        self.bottom_status.config(text="✅ Système vérifié - Prêt à démarrer")
    
    def log(self, message):
        """Ajoute un message aux logs"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        full_message = f"[{timestamp}] {message}"
        
        self.logs_text.insert(tk.END, full_message + "\n")
        self.logs_text.see(tk.END)
        self.root.update_idletasks()
        
        logger.info(message)
    
    def start_scraping(self):
        """Démarre le processus de scraping"""
        if self.is_running:
            return
            
        # Validation
        try:
            limit = int(self.limit_var.get())
            if limit <= 0:
                messagebox.showerror("Erreur", "La limite doit être un nombre positif")
                return
        except ValueError:
            messagebox.showerror("Erreur", "La limite doit être un nombre")
            return
            
        dept_file = self.dept_var.get()
        if not dept_file:
            messagebox.showerror("Erreur", "Veuillez sélectionner un département")
            return
            
        # Démarrage
        self.is_running = True
        self.stop_flag = False
        self.start_btn.config(state='disabled')
        self.stop_btn.config(state='normal')
        self.status_label.config(text="🔄 En cours...", fg='#FF9800')
        self.progress.config(value=0)
        
        # Thread de scraping
        self.current_thread = threading.Thread(target=self.run_scraping)
        self.current_thread.daemon = True
        self.current_thread.start()
    
    def run_scraping(self):
        """Exécute le processus de scraping"""
        try:
            self.log("🚀 === DÉMARRAGE DU SCRAPING ===")
            
            # Configuration
            limit = int(self.limit_var.get())
            dept_file = self.dept_var.get()
            mode = self.mode_var.get()
            csv_path = os.path.join(self.data_dir, dept_file)
            
            self.log(f"📁 Fichier: {dept_file}")
            self.log(f"📊 Limite: {limit} associations")
            self.log(f"🔧 Mode: {'Test (simulation)' if mode == 'test' else 'Réel (Selenium)'}")
            
            if not os.path.exists(csv_path):
                self.log(f"❌ Fichier non trouvé: {csv_path}")
                return
            
            # Lecture du CSV
            self.log("📋 Lecture du fichier CSV...")
            rnas = []
            with open(csv_path, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f, delimiter=';')
                for i, row in enumerate(reader):
                    if i >= limit:
                        break
                    rna = row.get('id', '').strip().strip('"')  # Enlever les guillemets
                    if rna:
                        rnas.append(rna)
            
            self.log(f"📋 {len(rnas)} RNA trouvés")
            
            if not rnas:
                self.log("❌ Aucun RNA trouvé")
                return
            
            # Initialisation du scraper si mode réel
            if mode == "real":
                self.log("🔧 Initialisation du moteur Selenium...")
                self.scraper_engine = ScraperEngine(self.chromedriver_path)
                if not self.scraper_engine.init_driver():
                    self.log("❌ Impossible d'initialiser Selenium")
                    return
                self.log("✅ Moteur Selenium prêt")
            
            # Scraping
            results = []
            total = len(rnas)
            
            for i, rna in enumerate(rnas):
                if self.stop_flag:
                    self.log("⏹️ Arrêt demandé")
                    break
                
                # Mise à jour de la progression
                progress = (i + 1) / total * 100
                self.root.after(0, lambda p=progress, c=i+1, t=total: self.update_progress(p, c, t))
                
                self.log(f"🔍 Scraping {i+1}/{total}: {rna}")
                
                if mode == "real" and self.scraper_engine:
                    # Scraping réel
                    result = self.scraper_engine.scrape_association(rna)
                    if result:
                        results.append(result)
                        self.log(f"✅ Succès: {result.get('nom', 'Nom non trouvé')}")
                    else:
                        self.log(f"❌ Échec pour {rna}")
                else:
                    # Mode test - simulation
                    result = {
                        'rna': rna,
                        'nom': f"Association Test {i+1}",
                        'adresse': f"123 Rue Test {i+1}, Test City",
                        'telephone': f"01.23.45.67.{i:02d}",
                        'email': f"contact{i+1}@test-association.fr",
                        'site_web': f"http://test-association-{i+1}.fr",
                        'objet': f"Objet de l'association test numéro {i+1}",
                        'date_creation': "2020-01-01",
                        'statut': "Active"
                    }
                    results.append(result)
                    self.log(f"✅ Simulation: {result['nom']}")
                
                # Pause entre les requêtes
                if mode == "real":
                    time.sleep(2)  # Pause plus longue pour le scraping réel
                else:
                    time.sleep(0.3)  # Pause courte pour la simulation
            
            # Sauvegarde
            if results and not self.stop_flag:
                self.log("💾 Sauvegarde des résultats...")
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_file = os.path.join(self.output_dir, f"scraping_{timestamp}.xlsx")
                
                try:
                    df = pd.DataFrame(results)
                    df.to_excel(output_file, index=False)
                    self.log(f"✅ {len(results)} résultats sauvegardés dans {output_file}")
                    self.root.after(0, lambda: self.bottom_status.config(text=f"✅ {len(results)} associations sauvegardées"))
                except Exception as e:
                    self.log(f"❌ Erreur sauvegarde: {e}")
            
            self.log("🎉 Scraping terminé avec succès!")
            
        except Exception as e:
            self.log(f"❌ Erreur durant le scraping: {e}")
            import traceback
            self.log(f"💥 Détails: {traceback.format_exc()}")
        finally:
            # Nettoyage
            if self.scraper_engine:
                self.scraper_engine.close()
                self.scraper_engine = None
            
            # Reset UI
            self.root.after(0, self.reset_ui)
    
    def update_progress(self, progress, current, total):
        """Met à jour la barre de progression"""
        self.progress.config(value=progress)
        self.progress_label.config(text=f"{current}/{total}")
        
    def stop_scraping(self):
        """Arrête le processus"""
        self.stop_flag = True
        self.log("⏹️ Arrêt en cours...")
        
    def reset_ui(self):
        """Remet l'interface en état initial"""
        self.is_running = False
        self.start_btn.config(state='normal')
        self.stop_btn.config(state='disabled')
        self.status_label.config(text="✅ Terminé", fg='#4CAF50')
        
    def open_results(self):
        """Ouvre le dossier des résultats"""
        try:
            os.startfile(self.output_dir)
            self.log(f"📁 Ouverture: {self.output_dir}")
        except Exception as e:
            self.log(f"❌ Erreur ouverture: {e}")
            
    def clear_logs(self):
        """Nettoie la zone de logs"""
        self.logs_text.delete(1.0, tk.END)
        self.log("🗑️ Logs nettoyés")
        
    def run(self):
        """Lance l'application"""
        try:
            self.log("🚀 Application lancée!")
            self.root.mainloop()
        except KeyboardInterrupt:
            self.log("👋 Fermeture de l'application")
        finally:
            if self.scraper_engine:
                self.scraper_engine.close()

def main():
    """Point d'entrée principal"""
    print("🚀 === SCRAPER D'ASSOCIATIONS - VERSION COMPLÈTE ===")
    app = ScraperAppComplete()
    app.run()

if __name__ == "__main__":
    main()