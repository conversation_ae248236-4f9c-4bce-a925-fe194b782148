#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test simple pour vérifier que tout fonctionne
"""

print("=== TEST SIMPLE ===")

try:
    import os
    print("✅ Import os: OK")
    
    import pandas as pd
    print("✅ Import pandas: OK")
    
    from selenium import webdriver
    print("✅ Import selenium: OK")
    
    from bs4 import BeautifulSoup
    print("✅ Import BeautifulSoup: OK")
    
    # Test chromedriver
    print(f"📁 ChromeDriver existe: {os.path.exists('./chromedriver.exe')}")
    
    # Test dossier data
    data_dir = "data/rna_waldec_20250701"
    print(f"📁 Dossier data existe: {os.path.exists(data_dir)}")
    
    if os.path.exists(data_dir):
        files = os.listdir(data_dir)
        print(f"📊 Nombre de fichiers CSV: {len(files)}")
        print(f"📋 Premier fichier: {files[0] if files else 'Aucun'}")
    
    print("✅ Tous les imports et vérifications: OK")
    
except Exception as e:
    print(f"❌ Erreur: {e}")
    import traceback
    traceback.print_exc()