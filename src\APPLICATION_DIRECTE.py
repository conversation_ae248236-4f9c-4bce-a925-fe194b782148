#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
APPLICATION DIRECTE - SCRAPER D'ASSOCIATIONS
Lance directement l'interface sans menu - VERSION ULTRA-SIMPLE
"""

import sys
import os

# Fix encodage Windows
if sys.platform == "win32":
    import locale
    try:
        locale.setlocale(locale.LC_ALL, 'fr_FR.UTF-8')
    except:
        pass

# Fix PyInstaller
if hasattr(sys, '_MEIPASS'):
    os.chdir(sys._MEIPASS)

# Redirection silencieuse pour EXE
try:
    if sys.stdout is None:
        sys.stdout = open(os.devnull, "w", encoding='utf-8')
    if sys.stderr is None:
        sys.stderr = open(os.devnull, "w", encoding='utf-8')
except:
    pass

def main():
    """Lance directement l'interface de scraping"""
    try:
        # Import et lancement direct de l'interface principale
        from gui import App
        
        # C<PERSON>er et lancer l'application
        app = App()
        app.mainloop()
        
    except Exception as e:
        # Fallback vers l'interface archivée si nécessaire
        try:
            import sys
            import os
            # Ajouter le dossier archive/old_versions au path temporairement
            archive_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'archive', 'old_versions')
            if archive_path not in sys.path:
                sys.path.insert(0, archive_path)
            
            from INTERFACE_TKINTER_STANDARD import ScraperAppStandard
            app = ScraperAppStandard()
            app.run()
        except Exception as e2:
            import tkinter as tk
            from tkinter import messagebox
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("Erreur", f"Impossible de lancer l'application.\n\nErreur 1: {e}\nErreur 2: {e2}")

if __name__ == "__main__":
    main() 