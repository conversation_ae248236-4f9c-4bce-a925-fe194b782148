#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test rapide pour vérifier la lecture du CSV
"""

import csv
import os

def test_csv_reading():
    """Teste la lecture du fichier CSV"""
    print("🔍 Test de lecture du fichier CSV...")
    
    csv_path = "data/rna_waldec_20250701/rna_waldec_20250701_dpt_975.csv"
    
    if not os.path.exists(csv_path):
        print(f"❌ Fichier non trouvé: {csv_path}")
        return
    
    print(f"✅ Fichier trouvé: {csv_path}")
    
    # Test lecture
    rnas = []
    with open(csv_path, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f, delimiter=';')
        
        # Afficher les headers
        print(f"📋 Colonnes: {reader.fieldnames}")
        
        for i, row in enumerate(reader):
            if i >= 5:  # Limiter à 5 pour test
                break
            
            # Méthode originale (avec problème)
            rna_old = row.get('id', '').strip()
            # Méthode corrigée
            rna_new = row.get('id', '').strip().strip('"')
            
            print(f"Ligne {i+1}:")
            print(f"  RNA brut: {repr(rna_old)}")
            print(f"  RNA nettoyé: {repr(rna_new)}")
            print(f"  Nom: {row.get('titre', '').strip().strip('\"')}")
            print()
            
            if rna_new:
                rnas.append(rna_new)
    
    print(f"✅ {len(rnas)} RNA trouvés:")
    for rna in rnas:
        print(f"  - {rna}")

if __name__ == "__main__":
    test_csv_reading()