#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 SCRAPER CORRIGÉ - VERSION SANS ERREURS NONETYPE
Gère correctement les réponses nulles et les éléments manquants
"""

import csv
import logging
import re
import time
import os
import zipfile
from pathlib import Path
from typing import Dict, List, Optional, Callable
import openpyxl
import json
import multiprocessing
import threading
from queue import Empty

from bs4 import BeautifulSoup

# Imports Selenium avec gestion d'erreurs pour PyInstaller
try:
    from selenium import webdriver
    from selenium.webdriver.chrome.service import Service as ChromeService
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.common.exceptions import TimeoutException, WebDriverException
except ImportError:
    # Fallback pour problèmes PyInstaller
    try:
        import selenium.webdriver as webdriver
        from selenium.webdriver.chrome.service import Service as ChromeService
        from selenium.webdriver.common.by import By
        from selenium.webdriver.support.ui import WebDriverWait
        from selenium.webdriver.support import expected_conditions as EC
        from selenium.common.exceptions import TimeoutException, WebDriverException
    except ImportError:
        print("⚠️ Selenium non trouvé - mode dégradé")
        webdriver = None

# Configuration du logging - fichier à la racine du projet
log_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'scraper.log')
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_path, encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ScraperFixed:
    """Scraper CORRIGÉ utilisant Selenium - GÈRE LES ERREURS NONETYPE."""

    def __init__(self, driver_path: str, proxy_config: Optional[Dict] = None):
        self.base_url = "https://www.data-asso.fr/annuaire/association"
        self.logger = logger
        self._driver_path = driver_path
        self.proxy_extension = None
        if proxy_config and proxy_config.get('host'):
            self.proxy_extension = self._create_proxy_extension(proxy_config)
        
        self.driver = self._init_driver()

    def _init_driver(self):
        """Initialise le WebDriver Chrome avec les bonnes options."""
        options = webdriver.ChromeOptions()
        
        # Options SPEED optimisées
        options.add_argument("--headless")
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--disable-gpu")
        options.add_argument("--disable-extensions")
        options.add_argument("--disable-images")  # SPEED
        # options.add_argument("--disable-javascript")  # SPEED
        # options.add_argument("--disable-css")  # SPEED
        options.add_argument("--disable-plugins")
        options.add_argument("--disable-web-security")
        options.add_argument("--allow-running-insecure-content")
        options.add_argument("--ignore-certificate-errors")
        options.add_argument("--ignore-ssl-errors")
        options.add_argument("--ignore-certificate-errors-spki-list")
        options.add_argument("--ignore-certificate-errors-ssl-security")
        options.add_argument("--window-size=1920,1080")
        options.add_argument("--disable-blink-features=AutomationControlled")
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        
        # Performance TURBO
        prefs = {
            "profile.managed_default_content_settings.images": 2,
            "profile.default_content_setting_values.notifications": 2,
            "profile.managed_default_content_settings.stylesheets": 2,
        }
        options.add_experimental_option("prefs", prefs)
        
        try:
            service = ChromeService(executable_path=self._driver_path)
            driver = webdriver.Chrome(service=service, options=options)
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            return driver
        except Exception as e:
            self.logger.error(f"❌ Erreur init driver: {e}")
            raise

    def get_page_source(self, url: str) -> Optional[str]:
        """
        🔧 VERSION CORRIGÉE - Gère les timeouts et erreurs correctement
        """
        max_retries = 2
        retry_delay = 1
        
        for attempt in range(max_retries):
            try:
                self.driver.get(url)
                WebDriverWait(self.driver, 15).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, "p[data-test-title='association']"))
                )
                return self.driver.page_source
                
            except TimeoutException:
                if attempt < max_retries - 1:
                    self.logger.warning(f"⏰ TIMEOUT SPEED {url} - retry {attempt + 1}/{max_retries}")
                    time.sleep(retry_delay)
                    continue
                else:
                    self.logger.error(f"❌ TIMEOUT FINAL SPEED {url} - SKIP pour vitesse")
                    return None
                    
            except WebDriverException as e:
                self.logger.error(f"❌ WebDriver error {url}: {e}")
                if "Session ID is null" in str(e) or "chrome not reachable" in str(e):
                    try:
                        self._reinit_driver()
                        continue
                    except Exception as reinit_error:
                        self.logger.error(f"❌ Erreur réinit driver: {reinit_error}")
                        return None
                elif attempt < max_retries - 1:
                    time.sleep(retry_delay)
                    continue
                else:
                    return None
            except Exception as e:
                self.logger.error(f"❌ Erreur FINALE SPEED {url}: {e}")
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)
                    continue
                else:
                    return None
        
        return None

    def get_association_details(self, rna: str) -> Optional[Dict]:
        """🔧 VERSION CORRIGÉE - Gère les éléments NULL/None correctement"""
        url = f"{self.base_url}/{rna}?docFields=documentsDac,documentsRna"
        html_content = self.get_page_source(url)

        if not html_content:
            return None

        try:
            soup = BeautifulSoup(html_content, 'lxml')
            
            # VÉRIFICATION CRITIQUE : S'assurer que soup n'est pas None
            if soup is None:
                self.logger.warning(f"⚠️ BeautifulSoup returned None for {rna}")
                return None
            
            details = {
                'rna': rna,
                'nom': self._extract_name_safe(soup),
                'adresse': self._extract_address_safe(soup),
                'adresse_gestion': self._extract_management_address_safe(soup),
                'telephone': self._extract_phone_safe(soup),
                'email': self._extract_email_safe(soup),
                'site_web': self._extract_website_safe(soup),
                'objet': self._extract_object_safe(soup),
                'date_creation': self._extract_creation_date_safe(soup),
                'statut': self._extract_status_safe(soup)
            }
            return details
            
        except Exception as e:
            self.logger.error(f"❌ Erreur parsing HTML pour {rna}: {e}")
            return None
        
    def _extract_name_safe(self, soup: BeautifulSoup) -> str:
        """🔧 EXTRACTION SÉCURISÉE du nom"""
        try:
            if soup is None:
                return ""
            title_elem = soup.find('p', {'data-test-title': 'association'})
            if title_elem is None:
                return ""
            return title_elem.get_text(strip=True) if title_elem else ""
        except Exception as e:
            self.logger.debug(f"Erreur extraction nom: {e}")
            return ""

    def _extract_address_safe(self, soup: BeautifulSoup) -> str:
        """🔧 EXTRACTION SÉCURISÉE de l'adresse"""
        try:
            if soup is None:
                return ""
            coordonnees_tab = soup.find('div', {'id': 'coordonnees'})
            if coordonnees_tab is None:
                return ""
            address_section = coordonnees_tab.find('h5', string=re.compile(r'Adresse(?! de gestion)'))
            if address_section is None:
                return ""
            address_div = address_section.find_next_sibling('div')
            if address_div is None:
                return ""
            return address_div.get_text(separator=', ', strip=True) if address_div else ""
        except Exception as e:
            self.logger.debug(f"Erreur extraction adresse: {e}")
            return ""

    def _extract_management_address_safe(self, soup: BeautifulSoup) -> str:
        """🔧 EXTRACTION SÉCURISÉE de l'adresse de gestion"""
        try:
            if soup is None:
                return ""
            coordonnees_tab = soup.find('div', {'id': 'coordonnees'})
            if coordonnees_tab is None:
                return ""
            management_section = coordonnees_tab.find('h5', string=re.compile(r'Adresse de gestion'))
            if management_section is None:
                return ""
            address_div = management_section.find_next_sibling('div')
            if address_div is None:
                return ""
            return address_div.get_text(separator=', ', strip=True) if address_div else ""
        except Exception as e:
            self.logger.debug(f"Erreur extraction adresse gestion: {e}")
            return ""

    def _extract_contact_info_safe(self, soup: BeautifulSoup, label: str) -> str:
        """🔧 EXTRACTION SÉCURISÉE des infos de contact"""
        try:
            if soup is None:
                return ""
            coordonnees_tab = soup.find('div', {'id': 'coordonnees'})
            if coordonnees_tab is None:
                return ""
            
            # CORRECTION CRITIQUE : Vérifier que tag n'est pas None avant .get()
            def safe_tag_check(tag):
                if tag is None:
                    return False
                if tag.name != 'div':
                    return False
                try:
                    text = tag.get_text(strip=True)
                    classes = tag.get('class', []) or []  # Protection contre None
                    return label in text and 'column' in classes
                except:
                    return False
            
            label_element = coordonnees_tab.find(safe_tag_check)
            
            if label_element is not None:
                value_element = label_element.find_next_sibling('div', class_='column')
                if value_element is not None:
                    text = value_element.get_text(strip=True)
                    return text if text and text != '-' else ""
            return ""
        except Exception as e:
            self.logger.debug(f"Erreur extraction contact {label}: {e}")
            return ""

    def _extract_phone_safe(self, soup: BeautifulSoup) -> str:
        """🔧 EXTRACTION SÉCURISÉE du téléphone"""
        return self._extract_contact_info_safe(soup, 'Téléphone')

    def _extract_email_safe(self, soup: BeautifulSoup) -> str:
        """🔧 EXTRACTION SÉCURISÉE de l'email"""
        return self._extract_contact_info_safe(soup, 'E-mail')

    def _extract_website_safe(self, soup: BeautifulSoup) -> str:
        """🔧 EXTRACTION SÉCURISÉE du site web"""
        return self._extract_contact_info_safe(soup, 'Site web')

    def _extract_object_safe(self, soup: BeautifulSoup) -> str:
        """🔧 EXTRACTION SÉCURISÉE de l'objet"""
        try:
            if soup is None:
                return ""
            activites_tab = soup.find('div', {'id': 'activites'})
            if activites_tab is None:
                return ""
            object_section = activites_tab.find('h5', string=re.compile(r'Objet'))
            if object_section is None:
                return ""
            object_p = object_section.find_next_sibling('p')
            if object_p is None:
                return ""
            return object_p.get_text(strip=True) if object_p else ""
        except Exception as e:
            self.logger.debug(f"Erreur extraction objet: {e}")
            return ""

    def _extract_creation_date_safe(self, soup: BeautifulSoup) -> str:
        """🔧 EXTRACTION SÉCURISÉE de la date de création"""
        try:
            if soup is None:
                return ""
            identite_tab = soup.find('div', {'id': 'identite'})
            if identite_tab is None:
                return ""
            date_section = identite_tab.find('h5', string=re.compile(r'Date de création'))
            if date_section is None:
                return ""
            date_p = date_section.find_next_sibling('p')
            if date_p is None:
                return ""
            return date_p.get_text(strip=True) if date_p else ""
        except Exception as e:
            self.logger.debug(f"Erreur extraction date: {e}")
            return ""

    def _extract_status_safe(self, soup: BeautifulSoup) -> str:
        """🔧 EXTRACTION SÉCURISÉE du statut"""
        try:
            if soup is None:
                return ""
            identite_tab = soup.find('div', {'id': 'identite'})
            if identite_tab is None:
                return ""
            status_tag = identite_tab.find('span', class_='tag')
            if status_tag is None:
                return ""
            return status_tag.get_text(strip=True) if status_tag else ""
        except Exception as e:
            self.logger.debug(f"Erreur extraction statut: {e}")
            return ""

    def _reinit_driver(self):
        """Réinitialise le WebDriver en cas de session fermée."""
        try:
            if self.driver:
                self.driver.quit()
        except:
            pass
        
        time.sleep(1)
        
        if hasattr(self, '_driver_path'):
            self.driver = self._init_driver()
        else:
            raise Exception("Impossible de réinitialiser: chemin du driver non disponible")

    def close_driver(self):
        """Ferme le driver proprement"""
        if self.driver:
            self.driver.quit()
        if self.proxy_extension and os.path.exists(self.proxy_extension):
            os.remove(self.proxy_extension)

# FONCTIONS PRINCIPALES POUR L'INTERFACE

def load_progress():
    """Charge les données de progression"""
    try:
        # Chemin vers progress.json à la racine du projet
        progress_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'progress.json')
        if os.path.exists(progress_path):
            with open(progress_path, 'r', encoding='utf-8') as f:
                return json.load(f)
    except:
        pass
    return {
        'processed_rnas': [],
        'total_associations': 0,
        'current_dept': '',
        'status': 'idle'
    }

def save_progress(data):
    """Sauvegarde les données de progression"""
    try:
        # Chemin vers progress.json à la racine du projet
        progress_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'progress.json')
        with open(progress_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
    except Exception as e:
        logger.error(f"Erreur sauvegarde progression: {e}")

def get_department_files(input_dir_path):
    """Récupère la liste des fichiers de départements CSV"""
    try:
        if not os.path.exists(input_dir_path):
            logger.error(f"Dossier introuvable: {input_dir_path}")
            return []
        
        files = []
        for filename in os.listdir(input_dir_path):
            if filename.startswith('rna_waldec_') and filename.endswith('.csv'):
                # Extraire le code département du nom de fichier
                dept_match = re.search(r'dpt_([^.]+)', filename)
                if dept_match:
                    files.append(filename)
        
        # Trier les fichiers par code département
        files.sort(key=lambda x: re.search(r'dpt_([^.]+)', x).group(1))
        logger.info(f"Trouvé {len(files)} fichiers de départements dans {input_dir_path}")
        return files
        
    except Exception as e:
        logger.error(f"Erreur lors de la lecture des fichiers de départements: {e}")
        return []

def run_scraper(params, stop_event=None, progress_queue=None):
    """
    Fonction principale de scraping compatible avec l'interface GUI
    
    Args:
        params: Dict avec les paramètres de scraping
        stop_event: Event pour arrêter le processus
        progress_queue: Queue pour les updates de progression
    """
    try:
        logger.info("🚀 Démarrage du scraper...")
        
        # Paramètres par défaut
        driver_path = params.get('driver_path', './chromedriver.exe')
        departments = params.get('departments', ['01'])
        max_workers = params.get('max_workers', 4)
        limit_per_dept = params.get('limit_per_dept', -1)
        output_file = params.get('output_file', 'associations_coordonnees.xlsx') # Get output file path
        input_directory = params.get('input_directory', '') # Get input directory

        # Créer le scraper
        scraper = ScraperFixed(driver_path)
        
        all_results = [] # To store all scraped data

        # Simuler le processus pour l'instant
        total_processed = 0
        
        for dept in departments:
            if stop_event and stop_event.is_set():
                break
                
            logger.info(f"📍 Traitement département {dept}")
            
            # Charger les données du département depuis data/
            # project_root = os.path.dirname(os.path.dirname(__file__))
            # dept_file = os.path.join(project_root, "data", "rna_waldec_20250701", dept)
            dept_file = os.path.join(input_directory, dept)

            if not os.path.exists(dept_file):
                logger.warning(f"❌ Fichier département {dept} introuvable: {dept_file}")
                continue
            
            # Lire le fichier CSV
            with open(dept_file, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f, delimiter=';')
                associations = list(reader)
            
            dept_count = len(associations)
            if limit_per_dept > 0:
                associations = associations[:limit_per_dept]
            
            logger.info(f"📊 {len(associations)} associations à traiter pour {dept}")
            
            # Traiter chaque association
            for i, assoc in enumerate(associations):
                if stop_event and stop_event.is_set():
                    break
                
                rna = assoc.get('id', '')
                if not rna:
                    continue
                
                # ***Remplacer la simulation par le scraping réel***
                associations_details = scraper.get_association_details(rna)

                if associations_details:
                    all_results.append(associations_details)
                    logger.info(f"✅ Scraping réussi pour RNA {rna}: {associations_details.get('nom', '')}")
                else:
                    logger.warning(f"⚠️ Pas de détails trouvés pour RNA {rna}")

                # time.sleep(0.1)  # Simulation
                total_processed += 1
                
                # Envoyer progression
                if progress_queue:
                    try:
                        progress_queue.put({
                            'type': 'progress',
                            'department': dept,
                            'processed': i + 1,
                            'total_dept': len(associations),
                            'total_processed': total_processed,
                            'current_rna': rna
                        })
                    except:
                        pass
        
        # Fermer le scraper
        scraper.close_driver()
        
        # ***Sauvegarder les résultats dans un fichier Excel***
        if all_results:
            try:
                import pandas as pd # Ensure pandas is imported if not already
                df = pd.DataFrame(all_results)
                df.to_excel(output_file, index=False) # Use the output_file from params
                logger.info(f"💾 Résultats sauvegardés dans {output_file}")
            except Exception as e:
                logger.error(f"❌ Erreur lors de la sauvegarde Excel: {e}")

        logger.info(f"✅ Scraping terminé! Total traité: {total_processed}")
        
        if progress_queue:
            try:
                progress_queue.put({
                    'type': 'complete',
                    'total_processed': total_processed
                })
            except:
                pass
                
    except Exception as e:
        logger.error(f"❌ Erreur dans run_scraper: {e}")
        if progress_queue:
            try:
                progress_queue.put({
                    'type': 'error',
                    'error': str(e)
                })
            except:
                pass

if __name__ == "__main__":
    # Test simple
    params = {
        'driver_path': './chromedriver.exe',
        'departments': ['01'],
        'max_workers': 1,
        'limit_per_dept': 5
    }
    run_scraper(params) 