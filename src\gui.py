import customtkinter as ctk
import threading
import os
import sys
import json
import multiprocessing
from queue import Empty
from scraper import run_scraper
import scraper as sc

# Fix for multiprocessing with PyInstaller: ensure stdout/stderr are not None at the very start
import sys
import os
if sys.stdout is None:
    sys.stdout = open(os.devnull, "w")
if sys.stderr is None:
    sys.stderr = open(os.devnull, "w")

def resource_path(relative_path):
    """ Get absolute path to resource, works for dev and for PyInstaller """
    try:
        # PyInstaller creates a temp folder and stores path in _MEIPASS
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(".")

    return os.path.join(base_path, relative_path)

# Configuration CustomTkinter avec gestion d'erreur pour screen distance
try:
    ctk.set_appearance_mode("dark")
    ctk.set_default_color_theme("dark-blue")
except Exception as e:
    # Fallback si problème d'affichage
    print(f"Avertissement CustomTkinter: {e}")
    try:
        ctk.set_appearance_mode("system")
        ctk.set_default_color_theme("blue")
    except:
        pass

class App(ctk.CTk):
    def __init__(self):
        super().__init__()

        # Threading and Queue setup - Initialized early to ensure availability
        self.manager = multiprocessing.Manager()
        self.stop_event = self.manager.Event()
        self.progress_queue = self.manager.Queue()
        self.progress_data = sc.load_progress()
        self.progress_thread = None

        self.title("Scraper & Data Manager")
        self.geometry("1100x700")

        # configure grid layout (1x2)
        self.grid_columnconfigure(1, weight=1)
        self.grid_rowconfigure(0, weight=1)
        
        # --- Left Navigation Frame ---
        self.navigation_frame = ctk.CTkFrame(self, corner_radius=0)
        self.navigation_frame.grid(row=0, column=0, sticky="nsew")
        self.navigation_frame.grid_rowconfigure(4, weight=1)

        self.navigation_frame_label = ctk.CTkLabel(self.navigation_frame, text="Scraper Control",
                                                  font=ctk.CTkFont(size=20, weight="bold"))
        self.navigation_frame_label.grid(row=0, column=0, padx=20, pady=20)

        self.start_button = ctk.CTkButton(self.navigation_frame, text="Démarrer / Reprendre", command=self.start_scraping)
        self.start_button.grid(row=1, column=0, padx=20, pady=10, sticky="ew")

        self.stop_button = ctk.CTkButton(self.navigation_frame, text="Arrêter", fg_color="#D32F2F", hover_color="#B71C1C", command=self.stop_scraping)
        self.stop_button.grid(row=2, column=0, padx=20, pady=10, sticky="ew")
        self.stop_button.configure(state="disabled")

        self.data_management_label = ctk.CTkLabel(self.navigation_frame, text="Gestion des Données",
                                                   font=ctk.CTkFont(size=16, weight="bold"))
        self.data_management_label.grid(row=3, column=0, padx=20, pady=(20,10))

        self.open_file_button = ctk.CTkButton(self.navigation_frame, text="Ouvrir Fichier de Sortie", command=self.open_output_file)
        self.open_file_button.grid(row=4, column=0, padx=20, pady=10, sticky="ew")

        # --- Main Content Area ---
        self.home_frame = ctk.CTkFrame(self, corner_radius=0, fg_color="transparent")
        self.home_frame.grid(row=0, column=1, sticky="nsew")
        self.home_frame.grid_columnconfigure(0, weight=1)
        self.home_frame.grid_rowconfigure(0, weight=1)

        self.tabview = ctk.CTkTabview(self.home_frame)
        self.tabview.grid(row=0, column=0, padx=20, pady=20, sticky="nsew")
        self.tabview.add("Logs")
        self.tabview.add("Paramètres")
        self.tabview.tab("Logs").grid_columnconfigure(0, weight=1)
        self.tabview.tab("Logs").grid_rowconfigure(0, weight=1)
        self.tabview.tab("Paramètres").grid_columnconfigure(0, weight=1)
        self.tabview.tab("Paramètres").grid_rowconfigure(1, weight=1)

        # --- Logs Tab ---
        self.textbox = ctk.CTkTextbox(self.tabview.tab("Logs"))
        self.textbox.grid(row=0, column=0, sticky="nsew")

        # --- Settings Tab ---
        # General settings
        self.settings_container = ctk.CTkFrame(self.tabview.tab("Paramètres"))
        self.settings_container.grid(row=0, column=0, padx=10, pady=10, sticky="ew")
        self.settings_container.grid_columnconfigure(1, weight=1)

        limit_label = ctk.CTkLabel(self.settings_container, text="Limite par département (-1 pour tout):")
        limit_label.grid(row=0, column=0, padx=10, pady=10, sticky="w")
        self.limit_entry = ctk.CTkEntry(self.settings_container, placeholder_text="-1")
        self.limit_entry.grid(row=0, column=1, padx=10, pady=10, sticky="ew")
        self.limit_entry.insert(0, "-1")

        output_label = ctk.CTkLabel(self.settings_container, text="Nom du fichier de sortie:")
        output_label.grid(row=1, column=0, padx=10, pady=10, sticky="w")
        self.output_file_entry = ctk.CTkEntry(self.settings_container, placeholder_text="associations_coordonnees.xlsx")
        self.output_file_entry.grid(row=1, column=1, padx=10, pady=10, sticky="ew")
        self.output_file_entry.insert(0, "associations_coordonnees.xlsx")

        processes_label = ctk.CTkLabel(self.settings_container, text="Nombre de processus parallèles:")
        processes_label.grid(row=2, column=0, padx=10, pady=10, sticky="w")
        self.processes_entry = ctk.CTkEntry(self.settings_container, placeholder_text="2")
        self.processes_entry.grid(row=2, column=1, padx=10, pady=10, sticky="ew")
        self.processes_entry.insert(0, "2")

        # Ajouter un avertissement pour les processus
        processes_warning = ctk.CTkLabel(self.settings_container, 
                                       text="⚠️ Recommandé: 2-3 processus max pour éviter les blocages", 
                                       font=ctk.CTkFont(size=11),
                                       text_color="#FFA726")
        processes_warning.grid(row=3, column=0, columnspan=2, padx=10, pady=5, sticky="w")

        # **NOUVEAU: Avertissement gros volumes**
        volume_warning = ctk.CTkLabel(self.settings_container, 
                                    text="🚨 GROS VOLUMES: 2.3M associations détectées!\n" +
                                         "Réduisez à 1 processus et 50 associations/test pour commencer",
                                    font=ctk.CTkFont(size=12, weight="bold"),
                                    text_color="#F44336")
        volume_warning.grid(row=4, column=0, columnspan=2, padx=10, pady=10, sticky="ew")

        self.reset_button = ctk.CTkButton(self.settings_container, text="Réinitialiser la progression", command=self.reset_progress)
        self.reset_button.grid(row=5, column=0, columnspan=2, padx=10, pady=20, sticky="ew")

        # Department selection
        self.dept_frame = ctk.CTkScrollableFrame(self.tabview.tab("Paramètres"), label_text="Sélection des départements")
        self.dept_frame.grid(row=1, column=0, padx=10, pady=10, sticky="nsew")

        self.dept_checkboxes = {}

        btn_frame = ctk.CTkFrame(self.tabview.tab("Paramètres"))
        btn_frame.grid(row=2, column=0, padx=10, pady=10, sticky="ew")
        btn_frame.grid_columnconfigure((0, 1, 2, 3), weight=1)
        
        select_all_btn = ctk.CTkButton(btn_frame, text="Tout sélectionner", command=self.select_all_depts)
        select_all_btn.grid(row=0, column=0, padx=5, pady=5, sticky="ew")
        deselect_all_btn = ctk.CTkButton(btn_frame, text="Tout désélectionner", command=self.deselect_all_depts)
        deselect_all_btn.grid(row=0, column=1, padx=5, pady=5, sticky="ew")
        
        # **BOUTON MODE NUIT**
        night_mode_btn = ctk.CTkButton(btn_frame, 
                                     text="🌙 MODE NUIT\n(Stable)", 
                                     command=self.setup_night_mode,
                                     fg_color="#2E7D32",
                                     hover_color="#1B5E20",
                                     font=ctk.CTkFont(size=11, weight="bold"))
        night_mode_btn.grid(row=0, column=2, padx=5, pady=5, sticky="ew")
        
        # **NOUVEAU: BOUTON TURBO SPEED**
        turbo_speed_btn = ctk.CTkButton(btn_frame, 
                                      text="⚡ TURBO SPEED\n(Max Vitesse)", 
                                      command=self.setup_turbo_speed,
                                      fg_color="#D32F2F",
                                      hover_color="#B71C1C",
                                      font=ctk.CTkFont(size=11, weight="bold"))
        turbo_speed_btn.grid(row=0, column=3, padx=5, pady=5, sticky="ew")

        # --- Progress Bar ---
        self.progressbar = ctk.CTkProgressBar(self.home_frame)
        self.progressbar.grid(row=1, column=0, padx=20, pady=(0, 10), sticky="ew")
        self.progressbar.set(0)

        # Threading and Queue setup
        self.scraper_thread = None # Re-added scraper_thread initialization

        # Schedule populate_department_list to run after interface is ready
        self.after(100, self.populate_department_list)  # Petit délai pour que l'interface soit prête

    def mark_department_as_done(self, filename, startup=False):
        """Changes a checkbox's state to visually indicate completion."""
        if filename in self.dept_checkboxes:
            checkbox = self.dept_checkboxes[filename]
            checkbox.configure(state="disabled", text_color="#00C853")
            # We don't need to change the variable on startup, just the visual state.
            if not startup:
                checkbox.get_variable_object().set("off")
                checkbox.select() # Keep it visually checked but disabled and green
                
    def check_progress_queue(self):
        try:
            while True:
                item = self.progress_queue.get_nowait()
                if item is None: # End signal
                    self.save_progress_data()
                    return
                
                action = item.get("action")
                if action == "log":
                    self.log_message(item.get("message", ""))
                elif action == "add_rna":
                    file = item.get("file")
                    rna = item.get("rna")
                    if file and rna:
                        if file not in self.progress_data["scraped_rnas"]:
                            self.progress_data["scraped_rnas"][file] = []
                        self.progress_data["scraped_rnas"][file].append(rna)
                elif action == "mark_department_complete":
                    file = item.get("file")
                    if file and file not in self.progress_data["completed_departments"]:
                        self.progress_data["completed_departments"].append(file)
                        self.mark_department_as_done(file)

        except Empty:
            pass
        
        # Keep checking
        self.after(250, self.check_progress_queue)

    def populate_department_list(self):
        # Chemin vers les données depuis le dossier src vers data/
        self.input_dir_path = resource_path("../data/rna_waldec_20250701")
        # Si cela ne fonctionne pas, essayer le chemin absolu
        if not os.path.exists(self.input_dir_path):
            self.input_dir_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "data", "rna_waldec_20250701")
        self.log_message(f"[DEBUG] Looking for department files in: {self.input_dir_path}")
        try:
            all_files = sc.get_department_files(self.input_dir_path)
            if not all_files:
                raise FileNotFoundError("Aucun fichier de département trouvé.")

            completed_files = set(self.progress_data.get("completed_departments", []))

            for filename in all_files:
                var = ctk.StringVar(value="on")
                cb = ctk.CTkCheckBox(self.dept_frame, text=filename, variable=var, onvalue="on", offvalue="off")
                cb.pack(anchor="w", padx=5)
                self.dept_checkboxes[filename] = cb # Store the widget

                if filename in completed_files:
                    self.mark_department_as_done(filename, startup=True)
                    
        except Exception as e:
            label = ctk.CTkLabel(self.dept_frame, text=f"Erreur: Impossible de lire les fichiers de département.\n{e}")
            label.pack()

    def select_all_depts(self):
        for checkbox in self.dept_checkboxes.values():
            if checkbox.cget("state") == "normal":
                checkbox.select()

    def deselect_all_depts(self):
        for checkbox in self.dept_checkboxes.values():
            if checkbox.cget("state") == "normal":
                checkbox.deselect()

    def setup_night_mode(self):
        """Configure automatiquement l'application pour un fonctionnement nocturne autonome."""
        try:
            # Configuration automatique des paramètres optimaux
            self.processes_entry.delete(0, "end")
            self.processes_entry.insert(0, "4")
            
            self.limit_entry.delete(0, "end") 
            self.limit_entry.insert(0, "-1")
            
            # Sélectionner tous les départements non terminés
            selected_count = 0
            for filename, checkbox in self.dept_checkboxes.items():
                if checkbox.cget("state") == "normal":  # Seulement les départements non terminés
                    checkbox.select()
                    selected_count += 1
            
            # Messages de configuration
            self.log_message("🌙 ===== MODE NUIT AUTONOME ACTIVÉ =====")
            self.log_message(f"⚙️ Configuration automatique:")
            self.log_message(f"   • Processus: 4 (performance maximale)")
            self.log_message(f"   • Limite: -1 (toutes les associations)")
            self.log_message(f"   • Départements: {selected_count} sélectionnés automatiquement")
            self.log_message("🔄 Sauvegarde automatique toutes les 50 associations")
            self.log_message("🛡️ Récupération automatique en cas d'erreur")
            self.log_message("⏰ Estimation: 400-800h de fonctionnement total")
            self.log_message("")
            self.log_message("💡 CONSEILS POUR NUIT COMPLÈTE:")
            self.log_message("   1. Fermez Excel et tous les navigateurs")
            self.log_message("   2. Lancez monitor_scraper.py dans un autre terminal")
            self.log_message("   3. Vérifiez que vous avez >8GB de RAM libre")
            self.log_message("   4. Le programme redémarrera automatiquement en cas de problème")
            self.log_message("")
            self.log_message("🚀 Prêt pour le lancement nocturne autonome!")
            
            # Changer la couleur du bouton start pour indiquer le mode nuit
            self.start_button.configure(text="🌙 DÉMARRER MODE NUIT", fg_color="#2E7D32")
            
        except Exception as e:
            self.log_message(f"❌ Erreur lors de la configuration du mode nuit: {e}")

    def setup_turbo_speed(self):
        """Configure automatiquement l'application pour VITESSE MAXIMALE."""
        try:
            # Configuration SPEED MAXIMALE
            self.processes_entry.delete(0, "end")
            self.processes_entry.insert(0, "4")  # 4 processus pour vitesse max
            
            self.limit_entry.delete(0, "end") 
            self.limit_entry.insert(0, "-1")  # Toutes les associations
            
            # Sélectionner tous les départements non terminés
            selected_count = 0
            for filename, checkbox in self.dept_checkboxes.items():
                if checkbox.cget("state") == "normal":
                    checkbox.select()
                    selected_count += 1
            
            # Messages de configuration SPEED
            self.log_message("⚡ ===== MODE TURBO SPEED ACTIVÉ =====")
            self.log_message(f"🚀 Configuration VITESSE MAXIMALE:")
            self.log_message(f"   • Processus: 4 (performance TURBO)")
            self.log_message(f"   • Limite: -1 (toutes les associations)")
            self.log_message(f"   • Départements: {selected_count} sélectionnés")
            self.log_message(f"   • Timeouts: RÉDUITS pour vitesse")
            self.log_message(f"   • Retries: MINIMISÉS pour rapidité")
            self.log_message(f"   • Pauses: ULTRA COURTES")
            self.log_message("")
            self.log_message("⚡ OPTIMISATIONS TURBO SPEED:")
            self.log_message("   🚀 Batches de 200 associations")
            self.log_message("   ⏰ Timeouts 15s (au lieu de 30s)")
            self.log_message("   🔄 2 retries max (au lieu de 3)")
            self.log_message("   ⏸️ Pauses 1s (au lieu de 3-5s)")
            self.log_message("   💾 Sauvegarde moins fréquente")
            self.log_message("   🎯 Chrome optimisé vitesse (pas d'images/JS)")
            self.log_message("")
            self.log_message("📊 ESTIMATION TURBO:")
            estimated_speed = selected_count * 15000  # 15k par département estimé
            estimated_hours = estimated_speed / 2000  # 2000 associations/h en mode turbo
            self.log_message(f"   📈 ~{estimated_speed:,} associations totales")
            self.log_message(f"   ⏰ ~{estimated_hours:.0f}h temps total estimé")
            self.log_message(f"   🚀 ~2000-3000 associations/h en TURBO")
            self.log_message("")
            self.log_message("⚠️ ATTENTION TURBO SPEED:")
            self.log_message("   • Mode AGGRESSIF - surveillance recommandée")
            self.log_message("   • Moins de vérifications = plus de risques")
            self.log_message("   • Arrêts plus fréquents possibles")
            self.log_message("   • Mais REPRISE automatique exacte")
            self.log_message("")
            self.log_message("🚀 PRÊT POUR VITESSE MAXIMALE!")
            
            # Changer la couleur du bouton start pour TURBO
            self.start_button.configure(text="⚡ DÉMARRER TURBO SPEED", fg_color="#D32F2F")
            
        except Exception as e:
            self.log_message(f"❌ Erreur configuration TURBO: {e}")

    def log_message(self, message):
        self.textbox.insert("end", message + "\n")
        self.textbox.see("end")
        self.update_idletasks()

    def update_progress(self, value):
        self.progressbar.set(value)
        self.update_idletasks()

    def start_scraping(self):
        self.stop_event.clear()
        self.start_button.configure(state="disabled")
        self.stop_button.configure(state="normal")
        
        # Détecter le mode
        button_text = self.start_button.cget("text")
        is_night_mode = button_text.startswith("🌙")
        is_turbo_mode = button_text.startswith("⚡")
        
        if is_turbo_mode:
            self.log_message("⚡ ===== DÉMARRAGE TURBO SPEED MAXIMALE =====")
        elif is_night_mode:
            self.log_message("🌙 ===== DÉMARRAGE MODE NUIT AUTONOME =====")
        else:
            self.log_message("🌅 Démarrage du scraping standard...")
        
        try:
            num_to_scrape = int(self.limit_entry.get())
        except ValueError:
            self.log_message("Erreur: La limite doit être un nombre entier.")
            num_to_scrape = -1

        try:
            num_processes = int(self.processes_entry.get())
            if num_processes < 1:
                self.log_message("❌ Erreur: Le nombre de processus doit être d'au moins 1.")
                num_processes = 1
            elif num_processes > 4:
                if not (is_night_mode or is_turbo_mode):  # Seulement avertir en mode standard
                    self.log_message("⚠️ Attention: Plus de 4 processus peut causer des problèmes de stabilité. Limitation à 4.")
                num_processes = 4
                
            # Mode spéciaux: validation
            if is_turbo_mode:
                if num_processes < 4:
                    self.log_message(f"⚡ Mode TURBO détecté: Ajustement à 4 processus pour VITESSE MAXIMALE")
                    num_processes = 4
                self.log_message(f"🚀 Mode TURBO: {num_processes} processus pour SPEED OPTIMAL")
            elif is_night_mode:
                if num_processes < 4:
                    self.log_message(f"🌙 Mode nuit détecté: Ajustement à 4 processus pour performance optimale")
                    num_processes = 4
                self.log_message(f"🚀 Mode nuit: {num_processes} processus configurés pour fonctionnement autonome")
                
        except ValueError:
            if is_turbo_mode:
                default_processes = 4
                mode_name = "TURBO"
            elif is_night_mode:
                default_processes = 4
                mode_name = "NUIT"
            else:
                default_processes = 2
                mode_name = "STANDARD"
            self.log_message(f"❌ Erreur: Le nombre de processus doit être un nombre entier. Utilisation de {default_processes} par défaut ({mode_name}).")
            num_processes = default_processes

        output_file = self.output_file_entry.get()
        if not output_file:
            output_file = "associations_coordonnees.xlsx"
            self.log_message(f"Nom de fichier vide, utilisation de la valeur par défaut: {output_file}")
        
        # S'assurer que le chemin du fichier de sortie est dans le dossier output/
        if getattr(sys, 'frozen', False):
            # Running as a bundled exe
            output_dir = os.path.dirname(sys.executable)
        else:
            # Running as a script depuis src/, aller vers la racine puis output/
            project_root = os.path.dirname(os.path.dirname(__file__))
            output_dir = os.path.join(project_root, 'output')
            
        # Créer le dossier output s'il n'existe pas
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        absolute_output_path = os.path.join(output_dir, output_file)
        self.log_message(f"Le fichier de sortie sera enregistré ici : {absolute_output_path}")

        selected_files = [filename for filename, var in self.dept_checkboxes.items() if var.get() == "on"]
    
        if not selected_files:
            self.log_message("Aucun département sélectionné. Veuillez en choisir au moins un dans l'onglet Paramètres.")
            self.start_button.configure(state="normal")
            self.stop_button.configure(state="disabled")
            return

        # Messages spéciaux selon le mode
        if is_turbo_mode:
            self.log_message(f"⚡ CONFIGURATION TURBO SPEED:")
            self.log_message(f"   📊 Départements: {len(selected_files)}")
            self.log_message(f"   ⚙️ Processus: {num_processes} TURBO")
            estimated_total = len(selected_files) * 22000
            estimated_hours_turbo = estimated_total / 2500  # 2500/h en mode turbo
            self.log_message(f"   📈 Associations estimées: {estimated_total:,}")
            self.log_message(f"   ⏰ Temps estimé TURBO: {estimated_hours_turbo:.0f}h")
            self.log_message("   🚀 Vitesse: MAXIMALE (~2500/h)")
            self.log_message("   ⚡ Optimisations: TOUTES activées")
            self.log_message("")
        elif is_night_mode:
            self.log_message(f"🌙 CONFIGURATION MODE NUIT:")
            self.log_message(f"   📊 Départements: {len(selected_files)}")
            self.log_message(f"   ⚙️ Processus: {num_processes}")
            self.log_message(f"   📈 Associations estimées: {len(selected_files) * 22000:,}")
            self.log_message(f"   ⏰ Temps estimé: {len(selected_files) * 20:.0f}-{len(selected_files) * 35:.0f}h")
            self.log_message("   🛡️ Récupération automatique: ACTIVÉE")
            self.log_message("   💾 Sauvegardes fréquentes: ACTIVÉES") 
            self.log_message("")

        input_directory = self.input_dir_path

        if getattr(sys, 'frozen', False):
            # When running as a bundled executable, look for chromedriver.exe next to the exe
            chrome_driver_path = os.path.join(os.path.dirname(sys.executable), "chromedriver.exe")
        else:
            # When running as a script, use the resource_path function
            chrome_driver_path = resource_path("chromedriver.exe")

        # Create params dictionary for run_scraper
        scraper_params = {
            'limit_per_dept': num_to_scrape,
            'output_file': absolute_output_path,
            'max_workers': num_processes,
            'departments': selected_files,
            'input_directory': input_directory,
            'driver_path': chrome_driver_path
        }

        self.scraper_thread = threading.Thread(
            target=run_scraper,
            args=(
                scraper_params, # Pass the params dictionary
                self.stop_event, 
                self.progress_queue
            )
        )
        self.scraper_thread.daemon = True
        self.scraper_thread.start()
        self.monitor_thread()
        self.check_progress_queue()
        
    def stop_scraping(self):
        if self.scraper_thread and self.scraper_thread.is_alive():
            self.log_message("Signal d'arrêt envoyé. Finalisation des tâches en cours...")
            self.stop_event.set()
            self.stop_button.configure(state="disabled")

    def monitor_thread(self):
        if self.scraper_thread and self.scraper_thread.is_alive():
            self.after(1000, self.monitor_thread)
        else:
            if self.scraper_thread is not None: # check if it was ever started
                self.log_message("Le thread de scraping est terminé.")
                self.start_button.configure(state="normal")
                self.stop_button.configure(state="disabled")
                self.scraper_thread = None
                # On envoie le signal de fin après un court délai pour laisser les derniers messages arriver
                self.after(500, lambda: self.progress_queue.put(None))
                
    def save_progress_data(self):
        self.log_message("Sauvegarde de la progression...")
        sc.save_progress(self.progress_data)
        self.log_message("Progression sauvegardée.")
        
    def open_output_file(self):
        output_file = self.output_file_entry.get() or "associations_coordonnees.xlsx"
        
        if getattr(sys, 'frozen', False):
            output_dir = os.path.dirname(sys.executable)
        else:
            # Running as a script depuis src/, aller vers output/
            project_root = os.path.dirname(os.path.dirname(__file__))
            output_dir = os.path.join(project_root, 'output')
        
        absolute_output_path = os.path.join(output_dir, output_file)

        try:
            if os.path.exists(absolute_output_path):
                os.startfile(absolute_output_path)
                self.log_message(f"Ouverture de {absolute_output_path}...")
            else:
                self.log_message(f"Le fichier {absolute_output_path} n'existe pas encore. Lancez d'abord le scraping.")
        except Exception as e:
            self.log_message(f"Impossible d'ouvrir le fichier: {e}")
            
    def reset_progress(self):
        try:
            self.progress_data = {"completed_departments": [], "scraped_rnas": {}}
            self.save_progress_data()
            self.log_message("Progression réinitialisée. Redémarrez l'application pour voir les changements.")
        except Exception as e:
            self.log_message(f"Erreur lors de la réinitialisation: {e}")


if __name__ == "__main__":
    # Fix for multiprocessing with PyInstaller: ensure stdout/stderr are not None
    if sys.stdout is None:
        sys.stdout = open(os.devnull, "w")
    if sys.stderr is None:
        sys.stderr = open(os.devnull, "w")

    multiprocessing.freeze_support()
    app = App()
    app.mainloop() 