#!/usr/bin/env python3
"""Test CSV final avec correction définitive"""

import csv
import os

# Test direct
csv_path = "data/rna_waldec_20250701/rna_waldec_20250701_dpt_975.csv"

print("=== TEST DIRECT CSV ===")
print(f"Fichier: {csv_path}")
print(f"Existe: {os.path.exists(csv_path)}")

if os.path.exists(csv_path):
    # Lire avec différentes méthodes
    print("\n=== MÉTHODE 1: Lecture ligne par ligne ===")
    with open(csv_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
        print(f"Nombre de lignes: {len(lines)}")
        print(f"Première ligne: {lines[0][:100]}")
        print(f"Deuxième ligne: {lines[1][:100]}")
    
    print("\n=== MÉTHODE 2: csv.reader ===")
    with open(csv_path, 'r', encoding='utf-8') as f:
        reader = csv.reader(f, delimiter=';')
        headers = next(reader)
        print(f"Headers: {headers}")
        
        # Trouver l'index de la colonne 'id'
        id_index = None
        for i, header in enumerate(headers):
            if header.strip() == 'id':
                id_index = i
                break
        
        print(f"Index colonne 'id': {id_index}")
        
        if id_index is not None:
            rnas = []
            for i, row in enumerate(reader):
                if i >= 3:  # Limiter à 3
                    break
                if len(row) > id_index:
                    rna = row[id_index].strip().strip('"')
                    print(f"RNA {i+1}: '{rna}'")
                    if rna:
                        rnas.append(rna)
            
            print(f"RNA trouvés: {rnas}")
    
    print("\n=== MÉTHODE 3: csv.DictReader avec debug ===")
    with open(csv_path, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f, delimiter=';')
        print(f"Fieldnames: {reader.fieldnames}")
        
        rnas = []
        for i, row in enumerate(reader):
            if i >= 3:
                break
            
            # Debug de toutes les clés
            print(f"Row {i+1} keys: {list(row.keys())}")
            
            # Essayer différentes variantes
            id_variations = ['id', ' id', 'id ', '"id"']
            rna_found = None
            
            for var in id_variations:
                if var in row:
                    rna_found = row[var].strip().strip('"')
                    print(f"  Trouvé avec clé '{var}': '{rna_found}'")
                    break
            
            if rna_found:
                rnas.append(rna_found)
        
        print(f"RNA finaux: {rnas}")

else:
    print("ERREUR: Fichier non trouvé")