#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 SCRAPER D'ASSOCIATIONS - VERSION FINALE ULTIME
Rassemble toutes les meilleures fonctionnalités de toutes les versions
- Mode TURBO Speed (vitesse maximale)
- Mode NUIT (autonome stable)
- Mode STANDARD (équilibré)
- Interface complète avec tous les départements
- Toutes les vérifications et sécurités
- Auto-correction des erreurs
- Reprise automatique
- Statistiques en temps réel
"""

import customtkinter as ctk
import threading
import os
import sys
import json
import multiprocessing
import time
from datetime import datetime
from queue import Empty
import tkinter as tk
from tkinter import messagebox

# Configuration des chemins et imports
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, 'src')
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

# Import des modules du projet avec gestion d'erreurs
try:
    from scraper import run_scraper
    import scraper as sc
except ImportError as e:
    print(f"⚠️ Erreur import direct: {e}")
    try:
        sys.path.append('src')
        from scraper import run_scraper
        import scraper as sc
        print("✅ Import depuis src/ réussi")
    except ImportError as e2:
        print(f"❌ Erreur critique: {e2}")
        print("💡 Solution: Utilisez SCRAPER_ASSOCIATIONS_FINAL.exe à la place")
        input("Appuyez sur Entrée pour fermer...")
        sys.exit(1)

# Fix encodage Windows
if sys.platform == "win32":
    import locale
    try:
        locale.setlocale(locale.LC_ALL, 'fr_FR.UTF-8')
    except:
        try:
            locale.setlocale(locale.LC_ALL, 'C.UTF-8')
        except:
            pass

# Fix PyInstaller
if sys.stdout is None:
    sys.stdout = open(os.devnull, "w", encoding='utf-8')
if sys.stderr is None:
    sys.stderr = open(os.devnull, "w", encoding='utf-8')

def resource_path(relative_path):
    """Chemin vers les ressources (fonctionne en dev et en exe)"""
    try:
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(".")
    return os.path.join(base_path, relative_path)

# Configuration thème
ctk.set_appearance_mode("dark")
ctk.set_default_color_theme("blue")

class ScraperFinal(ctk.CTk):
    """Application finale ultime - Rassemble toutes les fonctionnalités"""
    
    def __init__(self):
        super().__init__()
        
        # Variables de base
        self.manager = multiprocessing.Manager()
        self.stop_event = self.manager.Event()
        self.progress_queue = self.manager.Queue()
        self.progress_data = sc.load_progress()
        self.scraper_thread = None
        
        # Statistiques
        self.total_scraped = 0
        self.current_speed = 0
        self.start_time = time.time()
        self.scraping_active = False
        self.current_mode = "STANDARD"
        
        # Interface
        self.dept_checkboxes = {}
        self.input_dir_path = ""
        
        # Configuration fenêtre
        self.title("🚀 SCRAPER D'ASSOCIATIONS - VERSION FINALE ULTIME")
        self.geometry("1400x900")
        self.minsize(1200, 700)
        
        # Setup complet
        self.setup_interface()
        self.after(100, self.populate_department_list)
        self.after(200, self.show_welcome_message)
        
    def setup_interface(self):
        """Interface finale optimisée"""
        # Layout principal
        self.grid_columnconfigure(1, weight=1)
        self.grid_rowconfigure(0, weight=1)
        
        # Panneau gauche - Contrôles
        self.setup_control_panel()
        
        # Zone principale - Onglets
        self.setup_main_area()
        
    def setup_control_panel(self):
        """Panneau de contrôle optimisé"""
        self.control_frame = ctk.CTkFrame(self, width=300, corner_radius=0)
        self.control_frame.grid(row=0, column=0, sticky="nsew")
        self.control_frame.grid_propagate(False)
        
        # Titre
        title_label = ctk.CTkLabel(
            self.control_frame, 
            text="🚀 CONTRÔLE ULTIME",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        title_label.pack(pady=20)
        
        # Boutons principaux
        self.start_button = ctk.CTkButton(
            self.control_frame,
            text="▶️ DÉMARRER",
            command=self.start_scraping,
            height=50,
            font=ctk.CTkFont(size=16, weight="bold"),
            fg_color="#2E7D32",
            hover_color="#1B5E20"
        )
        self.start_button.pack(pady=10, padx=20, fill="x")
        
        self.stop_button = ctk.CTkButton(
            self.control_frame,
            text="⏹️ ARRÊTER",
            command=self.stop_scraping,
            height=40,
            fg_color="#D32F2F",
            hover_color="#B71C1C",
            state="disabled"
        )
        self.stop_button.pack(pady=5, padx=20, fill="x")
        
        # Modes de fonctionnement
        modes_frame = ctk.CTkFrame(self.control_frame)
        modes_frame.pack(pady=20, padx=20, fill="x")
        
        modes_label = ctk.CTkLabel(modes_frame, text="🎯 MODES", font=ctk.CTkFont(size=14, weight="bold"))
        modes_label.pack(pady=10)
        
        # Mode Standard
        standard_btn = ctk.CTkButton(
            modes_frame,
            text="🌅 STANDARD\n(Équilibré)",
            command=self.set_standard_mode,
            height=60,
            fg_color="#1976D2",
            hover_color="#1565C0"
        )
        standard_btn.pack(pady=5, fill="x")
        
        # Mode Nuit
        night_btn = ctk.CTkButton(
            modes_frame,
            text="🌙 MODE NUIT\n(Autonome)",
            command=self.set_night_mode,
            height=60,
            fg_color="#2E7D32",
            hover_color="#1B5E20"
        )
        night_btn.pack(pady=5, fill="x")
        
        # Mode Turbo
        turbo_btn = ctk.CTkButton(
            modes_frame,
            text="⚡ TURBO SPEED\n(Vitesse Max)",
            command=self.set_turbo_mode,
            height=60,
            fg_color="#D32F2F",
            hover_color="#B71C1C"
        )
        turbo_btn.pack(pady=5, fill="x")
        
        # Statistiques
        stats_frame = ctk.CTkFrame(self.control_frame)
        stats_frame.pack(pady=20, padx=20, fill="x")
        
        stats_label = ctk.CTkLabel(stats_frame, text="📊 STATISTIQUES", font=ctk.CTkFont(size=14, weight="bold"))
        stats_label.pack(pady=10)
        
        self.stats_text = ctk.CTkTextbox(stats_frame, height=120, font=ctk.CTkFont(size=11))
        self.stats_text.pack(pady=5, fill="x")
        
        # Boutons utilitaires
        self.reset_button = ctk.CTkButton(
            self.control_frame,
            text="🔄 RÉINITIALISER",
            command=self.reset_progress,
            fg_color="#FF6F00",
            hover_color="#E65100"
        )
        self.reset_button.pack(pady=10, padx=20, fill="x")
        
        self.open_file_button = ctk.CTkButton(
            self.control_frame,
            text="📂 OUVRIR RÉSULTATS",
            command=self.open_output_file,
            fg_color="#7B1FA2",
            hover_color="#6A1B9A"
        )
        self.open_file_button.pack(pady=5, padx=20, fill="x")
        
    def setup_main_area(self):
        """Zone principale avec onglets"""
        self.main_frame = ctk.CTkFrame(self, corner_radius=0, fg_color="transparent")
        self.main_frame.grid(row=0, column=1, sticky="nsew")
        self.main_frame.grid_columnconfigure(0, weight=1)
        self.main_frame.grid_rowconfigure(1, weight=1)
        
        # Barre de progression principale
        self.main_progress = ctk.CTkProgressBar(self.main_frame, height=20)
        self.main_progress.grid(row=0, column=0, padx=20, pady=10, sticky="ew")
        self.main_progress.set(0)
        
        # Onglets
        self.tabview = ctk.CTkTabview(self.main_frame)
        self.tabview.grid(row=1, column=0, padx=20, pady=(0, 20), sticky="nsew")
        
        # Onglet Logs
        self.setup_logs_tab()
        
        # Onglet Paramètres  
        self.setup_settings_tab()
        
        # Onglet Départements
        self.setup_departments_tab()
        
    def setup_logs_tab(self):
        """Onglet logs amélioré"""
        self.tabview.add("📋 Logs")
        logs_tab = self.tabview.tab("📋 Logs")
        logs_tab.grid_columnconfigure(0, weight=1)
        logs_tab.grid_rowconfigure(0, weight=1)
        
        self.log_textbox = ctk.CTkTextbox(
            logs_tab,
            font=ctk.CTkFont(family="Consolas", size=12),
            wrap="word"
        )
        self.log_textbox.grid(row=0, column=0, sticky="nsew", padx=10, pady=10)
        
    def setup_settings_tab(self):
        """Onglet paramètres complet"""
        self.tabview.add("⚙️ Paramètres")
        settings_tab = self.tabview.tab("⚙️ Paramètres")
        settings_tab.grid_columnconfigure(0, weight=1)
        
        # Frame des paramètres
        params_frame = ctk.CTkFrame(settings_tab)
        params_frame.grid(row=0, column=0, padx=10, pady=10, sticky="ew")
        params_frame.grid_columnconfigure(1, weight=1)
        
        # Limite par département
        limit_label = ctk.CTkLabel(params_frame, text="Limite par département (-1 = tout):")
        limit_label.grid(row=0, column=0, padx=10, pady=10, sticky="w")
        self.limit_entry = ctk.CTkEntry(params_frame, placeholder_text="-1")
        self.limit_entry.grid(row=0, column=1, padx=10, pady=10, sticky="ew")
        self.limit_entry.insert(0, "-1")
        
        # Nombre de processus
        processes_label = ctk.CTkLabel(params_frame, text="Processus parallèles (1-4):")
        processes_label.grid(row=1, column=0, padx=10, pady=10, sticky="w")
        self.processes_entry = ctk.CTkEntry(params_frame, placeholder_text="2")
        self.processes_entry.grid(row=1, column=1, padx=10, pady=10, sticky="ew")
        self.processes_entry.insert(0, "2")
        
        # Fichier de sortie
        output_label = ctk.CTkLabel(params_frame, text="Fichier de sortie:")
        output_label.grid(row=2, column=0, padx=10, pady=10, sticky="w")
        self.output_file_entry = ctk.CTkEntry(params_frame, placeholder_text="associations_coordonnees.xlsx")
        self.output_file_entry.grid(row=2, column=1, padx=10, pady=10, sticky="ew")
        self.output_file_entry.insert(0, "associations_coordonnees.xlsx")
        
    def setup_departments_tab(self):
        """Onglet départements optimisé"""
        self.tabview.add("🗺️ Départements")
        dept_tab = self.tabview.tab("🗺️ Départements")
        dept_tab.grid_columnconfigure(0, weight=1)
        dept_tab.grid_rowconfigure(1, weight=1)
        
        # Contrôles sélection
        controls_frame = ctk.CTkFrame(dept_tab)
        controls_frame.grid(row=0, column=0, padx=10, pady=10, sticky="ew")
        controls_frame.grid_columnconfigure((0, 1, 2, 3), weight=1)
        
        select_all_btn = ctk.CTkButton(controls_frame, text="✅ Tout sélectionner", command=self.select_all_depts)
        select_all_btn.grid(row=0, column=0, padx=5, pady=5, sticky="ew")
        
        deselect_all_btn = ctk.CTkButton(controls_frame, text="❌ Tout désélectionner", command=self.deselect_all_depts)
        deselect_all_btn.grid(row=0, column=1, padx=5, pady=5, sticky="ew")
        
        # Liste des départements
        self.dept_frame = ctk.CTkScrollableFrame(dept_tab, label_text="📋 Sélection des départements (104 disponibles)")
        self.dept_frame.grid(row=1, column=0, padx=10, pady=10, sticky="nsew")
        
    def populate_department_list(self):
        """Chargement des départements avec vérifications"""
        try:
            # Déterminer le chemin des données
            possible_paths = [
                os.path.join(os.path.dirname(__file__), "data", "rna_waldec_20250701"),
                os.path.join("data", "rna_waldec_20250701"),
                resource_path("data/rna_waldec_20250701"),
                "rna_waldec_20250701"
            ]
            
            self.input_dir_path = None
            for path in possible_paths:
                if os.path.exists(path):
                    self.input_dir_path = path
                    break
            
            if not self.input_dir_path:
                self.log_message("❌ Dossier de données introuvable !")
                return
            
            self.log_message(f"📁 Données trouvées: {self.input_dir_path}")
            
            # Charger les fichiers
            all_files = sc.get_department_files(self.input_dir_path)
            if not all_files:
                self.log_message("❌ Aucun fichier de département trouvé")
                return
            
            self.log_message(f"✅ {len(all_files)} départements chargés")
            
            # Créer les checkboxes
            completed_files = set(self.progress_data.get("completed_departments", []))
            
            for filename in all_files:
                # Extraire le code département
                dept_code = filename.replace("rna_waldec_20250701_dpt_", "").replace(".csv", "")
                
                checkbox = ctk.CTkCheckBox(
                    self.dept_frame,
                    text=f"{dept_code} - {filename}",
                    onvalue=True,
                    offvalue=False
                )
                checkbox.pack(anchor="w", padx=10, pady=2)
                
                # Sélectionner par défaut
                checkbox.select()
                
                # Marquer comme terminé si nécessaire
                if filename in completed_files:
                    checkbox.configure(state="disabled", text_color="#00C853")
                
                self.dept_checkboxes[filename] = checkbox
                
        except Exception as e:
            self.log_message(f"❌ Erreur chargement départements: {e}")
    
    def show_welcome_message(self):
        """Message de bienvenue"""
        self.log_message("🚀 SCRAPER D'ASSOCIATIONS - VERSION FINALE ULTIME")
        self.log_message("=" * 60)
        self.log_message("✨ Toutes les fonctionnalités réunies :")
        self.log_message("   🌅 Mode STANDARD - Équilibré et stable")
        self.log_message("   🌙 Mode NUIT - Autonome pour longues sessions")
        self.log_message("   ⚡ Mode TURBO - Vitesse maximale")
        self.log_message("   📊 Statistiques en temps réel")
        self.log_message("   🔄 Reprise automatique")
        self.log_message("   🛡️ Auto-correction des erreurs")
        self.log_message("")
        self.log_message("🎯 Choisissez votre mode et démarrez !")
        self.update_stats()
        
    def set_standard_mode(self):
        """Mode standard équilibré"""
        self.current_mode = "STANDARD"
        self.start_button.configure(
            text="🌅 DÉMARRER STANDARD",
            fg_color="#1976D2",
            hover_color="#1565C0"
        )
        self.processes_entry.delete(0, tk.END)
        self.processes_entry.insert(0, "2")
        self.log_message("🌅 Mode STANDARD activé - Configuration équilibrée")
        
    def set_night_mode(self):
        """Mode nuit autonome"""
        self.current_mode = "NUIT"
        self.start_button.configure(
            text="🌙 DÉMARRER MODE NUIT",
            fg_color="#2E7D32",
            hover_color="#1B5E20"
        )
        self.processes_entry.delete(0, tk.END)
        self.processes_entry.insert(0, "1")
        self.log_message("🌙 Mode NUIT activé - Fonctionnement autonome stable")
        
    def set_turbo_mode(self):
        """Mode turbo vitesse maximale"""
        self.current_mode = "TURBO"
        self.start_button.configure(
            text="⚡ DÉMARRER TURBO SPEED",
            fg_color="#D32F2F",
            hover_color="#B71C1C"
        )
        self.processes_entry.delete(0, tk.END)
        self.processes_entry.insert(0, "4")
        self.log_message("⚡ Mode TURBO activé - Vitesse MAXIMALE (surveillance recommandée)")
        
    def select_all_depts(self):
        """Sélectionner tous les départements"""
        for checkbox in self.dept_checkboxes.values():
            if checkbox.cget("state") == "normal":
                checkbox.select()
        self.log_message("✅ Tous les départements sélectionnés")
        
    def deselect_all_depts(self):
        """Désélectionner tous les départements"""
        for checkbox in self.dept_checkboxes.values():
            if checkbox.cget("state") == "normal":
                checkbox.deselect()
        self.log_message("❌ Tous les départements désélectionnés")
        
    def start_scraping(self):
        """Démarrage du scraping avec mode sélectionné"""
        # Vérifications préalables
        selected_files = [filename for filename, checkbox in self.dept_checkboxes.items() 
                         if checkbox.get() and checkbox.cget("state") == "normal"]
        
        if not selected_files:
            self.log_message("❌ Aucun département sélectionné !")
            return
            
        # Configuration interface
        self.stop_event.clear()
        self.start_button.configure(state="disabled")
        self.stop_button.configure(state="normal")
        self.scraping_active = True
        self.start_time = time.time()
        
        # Messages selon le mode
        if self.current_mode == "TURBO":
            self.log_message("⚡ ===== DÉMARRAGE TURBO SPEED MAXIMALE =====")
            self.log_message(f"🚀 {len(selected_files)} départements en mode TURBO")
            estimated_total = len(selected_files) * 22000
            estimated_hours = estimated_total / 2500
            self.log_message(f"⏰ Temps estimé: {estimated_hours:.1f}h (~2500/h)")
        elif self.current_mode == "NUIT":
            self.log_message("🌙 ===== DÉMARRAGE MODE NUIT AUTONOME =====")
            self.log_message(f"🛡️ {len(selected_files)} départements en mode NUIT")
            self.log_message("💤 Fonctionnement autonome avec récupération automatique")
        else:
            self.log_message("🌅 ===== DÉMARRAGE MODE STANDARD =====")
            self.log_message(f"⚖️ {len(selected_files)} départements en mode équilibré")
        
        # Paramètres
        try:
            limit = int(self.limit_entry.get())
        except:
            limit = -1
            
        try:
            processes = int(self.processes_entry.get())
        except:
            processes = 2
            
        # Configuration pour l'exe
        if getattr(sys, 'frozen', False):
            chrome_driver_path = os.path.join(os.path.dirname(sys.executable), "chromedriver.exe")
            output_dir = os.path.dirname(sys.executable)
        else:
            chrome_driver_path = resource_path("chromedriver.exe")
            output_dir = os.path.join(os.path.dirname(__file__), 'output')
            
        # Créer dossier output
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
            
        output_file = os.path.join(output_dir, self.output_file_entry.get())
        
        # Paramètres du scraper
        params = {
            'driver_path': chrome_driver_path,
            'departments': [f.replace('rna_waldec_20250701_dpt_', '').replace('.csv', '') for f in selected_files],
            'max_workers': processes,
            'limit_per_dept': limit,
            'output_file': output_file,
            'input_directory': self.input_dir_path,
            'mode': self.current_mode
        }
        
        # Lancer le thread
        self.scraper_thread = threading.Thread(
            target=run_scraper,
            args=(params, self.stop_event, self.progress_queue)
        )
        self.scraper_thread.daemon = True
        self.scraper_thread.start()
        
        # Monitoring
        self.monitor_scraping()
        self.check_progress_queue()
        
    def stop_scraping(self):
        """Arrêt du scraping"""
        self.log_message("⏹️ Arrêt demandé...")
        self.stop_event.set()
        self.stop_button.configure(state="disabled")
        
    def monitor_scraping(self):
        """Monitoring du thread de scraping"""
        if self.scraper_thread and self.scraper_thread.is_alive():
            self.after(1000, self.monitor_scraping)
        else:
            if self.scraping_active:
                self.log_message("✅ Scraping terminé")
                self.start_button.configure(state="normal")
                self.stop_button.configure(state="disabled")
                self.scraping_active = False
                self.update_stats()
                
    def check_progress_queue(self):
        """Vérification de la queue de progression"""
        try:
            while True:
                item = self.progress_queue.get_nowait()
                if item is None:
                    return
                    
                if item.get('type') == 'progress':
                    self.total_scraped = item.get('total_processed', 0)
                    progress = item.get('processed', 0) / max(item.get('total_dept', 1), 1)
                    self.main_progress.set(progress)
                    self.update_stats()
                    
                elif item.get('type') == 'log':
                    self.log_message(item.get('message', ''))
                    
        except Empty:
            pass
            
        if self.scraping_active:
            self.after(500, self.check_progress_queue)
            
    def update_stats(self):
        """Mise à jour des statistiques"""
        if self.scraping_active and self.start_time:
            elapsed = time.time() - self.start_time
            speed = self.total_scraped / elapsed if elapsed > 0 else 0
            
            stats = f"""📊 STATISTIQUES TEMPS RÉEL
⏱️ Temps: {elapsed/3600:.1f}h
📈 Traités: {self.total_scraped:,}
🚀 Vitesse: {speed:.1f}/h
🎯 Mode: {self.current_mode}
🔋 Status: {'🟢 ACTIF' if self.scraping_active else '⏸️ ARRÊTÉ'}"""
        else:
            selected_count = sum(1 for cb in self.dept_checkboxes.values() if cb.get())
            stats = f"""📊 CONFIGURATION
🗺️ Départements: {selected_count}/{len(self.dept_checkboxes)}
🎯 Mode: {self.current_mode}  
📁 Résultats: output/
🔋 Status: ⏸️ PRÊT"""
            
        self.stats_text.delete("0.0", tk.END)
        self.stats_text.insert("0.0", stats)
        
    def reset_progress(self):
        """Réinitialisation"""
        if messagebox.askyesno("Confirmation", "Réinitialiser la progression ?"):
            self.progress_data = {"completed_departments": [], "scraped_rnas": {}}
            sc.save_progress(self.progress_data)
            self.log_message("🔄 Progression réinitialisée")
            
    def open_output_file(self):
        """Ouvrir le fichier de résultats"""
        if getattr(sys, 'frozen', False):
            output_dir = os.path.dirname(sys.executable)
        else:
            output_dir = os.path.join(os.path.dirname(__file__), 'output')
            
        output_file = os.path.join(output_dir, self.output_file_entry.get())
        
        if os.path.exists(output_file):
            os.startfile(output_file)
            self.log_message(f"📂 Ouverture: {output_file}")
        else:
            self.log_message(f"❌ Fichier introuvable: {output_file}")
            
    def log_message(self, message):
        """Ajout de message dans les logs"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"
        
        self.log_textbox.insert(tk.END, formatted_message + "\n")
        self.log_textbox.see(tk.END)
        self.update_idletasks()

def main():
    """Fonction principale"""
    try:
        app = ScraperFinal()
        app.mainloop()
    except Exception as e:
        messagebox.showerror("Erreur", f"Erreur fatale: {e}")

if __name__ == "__main__":
    main() 