print("DEBUG: Script lance")
print("DEBUG: Test simple")

try:
    import csv
    print("DEBUG: CSV importe")
    
    import os
    print("DEBUG: OS importe")
    
    csv_path = "data/rna_waldec_20250701/rna_waldec_20250701_dpt_975.csv"
    print(f"DEBUG: Chemin = {csv_path}")
    
    exists = os.path.exists(csv_path)
    print(f"DEBUG: Fichier existe = {exists}")
    
    if exists:
        with open(csv_path, 'r', encoding='utf-8') as f:
            print("DEBUG: Fichier ouvert")
            first_line = f.readline()
            print(f"DEBUG: Premiere ligne = {first_line[:100]}")
            
except Exception as e:
    print(f"DEBUG: ERREUR = {e}")
    import traceback
    traceback.print_exc()

print("DEBUG: Fin script")