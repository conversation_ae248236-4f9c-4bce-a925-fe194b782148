# 🔧 Outil de Scraping d'Associations avec Selenium

Cet outil permet d'extraire les coordonnées et les informations de contact des associations françaises à partir du site [data-asso.fr](https://www.data-asso.fr/annuaire). Il utilise Selenium pour piloter un navigateur Chrome, garantissant ainsi la récupération des données même sur les pages à contenu dynamique.

## ✨ Fonctionnalités

- **Interface graphique moderne** avec CustomTkinter
- **Extraction automatique** des numéros RNA depuis les fichiers CSV
- **Scraping robuste** avec système de retry automatique
- **Multiprocessing** pour accélérer le traitement
- **Système de progression** avec sauvegarde/reprise automatique
- **Gestion d'erreurs avancée** avec récupération automatique
- **Sauvegarde au format Excel** avec gestion des conflits
- **Logging détaillé** pour diagnostic des problèmes
- **🆕 Optimisé pour GROS VOLUMES** (2.3M associations)

## 🚨 **ATTENTION GROS VOLUMES !**

Cette base contient **2,3 MILLIONS d'associations** (104 départements × 22K associations). 

**➡️ LISEZ ABSOLUMENT le [Guide Gros Volumes](guide_gros_volumes.md) avant de commencer !**

## 📋 Prérequis

1. **Python 3.7+**
2. **Google Chrome** installé
3. **ChromeDriver** correspondant à votre version de Chrome
4. **🆕 RAM minimum : 8GB (16GB recommandé pour gros volumes)**
5. **🆕 Espace disque : 10GB libre minimum**

## 🚀 Installation

### 1. Téléchargez le ChromeDriver :
- Vérifiez votre version de Chrome : `chrome://version/`
- Téléchargez le ChromeDriver correspondant : [Chrome for Testing](https://googlechromelabs.github.io/chrome-for-testing/)
- Placez `chromedriver.exe` à la racine du projet

### 2. Installez les dépendances :
```bash
pip install -r requirements.txt
```

### 3. Vérifiez votre configuration :
```bash
python chromedriver_check.py
```

## 🎯 Utilisation

### ⚠️ **PREMIÈRE UTILISATION - GROS VOLUMES**
```bash
# 1. Démarrer le monitoring
python monitor_scraper.py

# 2. Dans un autre terminal, démarrer l'interface
python gui.py

# 3. Configuration OBLIGATOIRE pour test :
#    - Processus: 1
#    - Limite: 50
#    - Sélectionner 1 seul département
```

### Interface Graphique (Recommandé)
```bash
python gui.py
```

1. **🚨 ATTENTION :** Lisez l'avertissement gros volumes dans l'interface
2. **Configurez les paramètres** conservativement (1 processus, 50 associations)
3. **Sélectionnez 1 département** pour commencer
4. **Surveillez la mémoire** avec le monitoring

### Mode Console
```bash
python scraper.py
```

## ⚠️ Problèmes Courants et Solutions

### 🔴 Problème 1: Erreur de Permission Excel
**Symptôme :** `Permission denied: associations_coordonnees.xlsx`

**Solutions :**
- ✅ **Fermez Excel** complètement avant de lancer le scraper
- ✅ Le programme créera automatiquement un fichier de backup si nécessaire
- ✅ Changez le nom du fichier de sortie dans les paramètres

### 🔴 Problème 2: Sessions WebDriver Fermées
**Symptôme :** `invalid session id: session deleted`

**Solutions appliquées :**
- ✅ **Système de retry** automatique (3 tentatives)
- ✅ **Réinitialisation** automatique du WebDriver
- ✅ **Configuration Chrome optimisée** pour la stabilité
- ✅ **Gestion mémoire** améliorée

### 🔴 Problème 3: Surcharge du Système (PRINCIPAL)
**Symptôme :** Arrêt automatique, mémoire saturée, trop de processus Chrome

**Solutions appliquées :**
- ✅ **Traitement par batches** de 100 associations maximum
- ✅ **Monitoring mémoire** automatique avec alertes
- ✅ **Cache RNA intelligent** pour éviter de recharger Excel
- ✅ **Limitation automatique** à 2 processus pour gros volumes
- ✅ **Timeouts adaptés** aux gros volumes (30min par batch)

## 📊 Configuration Recommandée

| Paramètre | Petits Volumes | Gros Volumes (2.3M) | Remarque |
|-----------|----------------|---------------------|----------|
| **Processus parallèles** | 2-3 | **1-2 MAX** | Plus = crash |
| **Limite par département** | -1 (tout) | **50 pour test** | Puis augmenter |
| **Départements simultanés** | Tous | **1-5 MAX** | Par phase |
| **Fichier de sortie** | `associations_coordonnees.xlsx` | Idem + cache | Cache auto |

## 🔍 Diagnostic des Problèmes

### Étape 1: Vérifiez votre configuration
```bash
python chromedriver_check.py
```

### Étape 2: Lancez le monitoring (OBLIGATOIRE pour gros volumes)
```bash
python monitor_scraper.py
```

### Étape 3: Consultez les logs
- **Fichier :** `scraper.log`
- **Interface :** Onglet "Logs" dans l'application
- **Monitoring :** Alertes en temps réel

### Étape 4: Réinitialisez si nécessaire
- Cliquez sur "Réinitialiser la progression" dans l'interface
- Ou supprimez `progress.json`

## 📁 Structure du Projet

```
scrap3/
├── gui.py                          # Interface graphique principale
├── scraper.py                      # Moteur de scraping (optimisé gros volumes)
├── monitor_scraper.py              # 🆕 Monitoring temps réel
├── chromedriver_check.py           # Script de diagnostic
├── guide_gros_volumes.md           # 🆕 Guide spécial gros volumes
├── chromedriver.exe                # Driver Chrome (à télécharger)
├── requirements.txt                # Dépendances Python (+ psutil)
├── progress.json                   # Sauvegarde de progression
├── scraper.log                     # Logs détaillés
├── associations_coordonnees.xlsx   # Résultats (généré)
├── associations_coordonnees_rna_cache.txt # 🆕 Cache pour optimisation
└── rna_waldec_20250701/           # Données RNA sources (2.3M associations!)
    ├── rna_waldec_20250701_dpt_01.csv (22K associations)
    ├── rna_waldec_20250701_dpt_02.csv
    └── ... (104 départements)
```

## 🛠️ Améliorations v2.0 - GROS VOLUMES

### ✅ **Traitement par Batches**
- Division automatique en batches de 100 associations
- Pause entre batches pour éviter la surcharge
- Timeout de 30 minutes par batch

### ✅ **Cache RNA Intelligent**
- Fichier cache `.txt` pour éviter de recharger Excel
- Reconstruction automatique si corrompu
- Optimisation mémoire drastique

### ✅ **Monitoring Temps Réel**
- Script dédié `monitor_scraper.py`
- Alertes automatiques (mémoire, CPU, Chrome)
- Rapports détaillés avec historique

### ✅ **Gestion Mémoire Avancée**
- Vérification automatique avant chaque batch
- Pause forcée si mémoire > 85%
- Arrêt de sécurité si mémoire > 95%

### ✅ **Interface Adaptée**
- Avertissements pour gros volumes
- Suggestions de configuration
- Limitation automatique des processus

## 🆘 Support GROS VOLUMES

Si vous rencontrez des problèmes avec les 2.3M associations :

1. **📖 LISEZ le guide :** `guide_gros_volumes.md`
2. **🔍 Lancez le diagnostic :** `python chromedriver_check.py`
3. **📊 Surveillez en temps réel :** `python monitor_scraper.py`
4. **🚨 Commencez petit :** 1 processus, 50 associations, 1 département
5. **📈 Augmentez progressivement :** Une fois la stabilité confirmée

## ⏱️ **Estimation Temps - GROS VOLUMES**

```
• 1 association     = 3-5 secondes
• 100 associations  = 5-8 minutes
• 1 département     = 15-30 heures  
• Tous départements = 400-800 heures (17-33 jours)
```

**💡 Stratégie recommandée :** Scraping par phases sur plusieurs semaines avec surveillance continue.

---

**🎯 POUR LES GROS VOLUMES : La patience et la surveillance sont les clés du succès !**

**📋 Checklist de démarrage :** [Guide Gros Volumes](guide_gros_volumes.md)