#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour mettre à jour les données d'associations avec les informations scrapées
"""

import csv
import re
from scraper_final_reel import Scrap<PERSON><PERSON><PERSON>Reel

def update_association_w011000002():
    """Met à jour les données de l'association W011000002 avec les vraies données scrapées"""
    
    # Données Markdown réelles de W011000002
    markdown_data = '''Data-Asso

[Retour à la liste des associations](https://www.data-asso.fr/annuaire)

ASSOCIATION UNE NOTE DE PARTAGE - COMPAGNIE DES PAPILLONS BLEUS


Partager


Association active

Éligible CEC


##### Sigle

UNE NOTE DE PARTAGE

##### Date de création

22/03/2007

##### Date de dissolution

07/01/0001

##### Date de la dernière déclaration (RNA)

29/04/2022

N° RNA
W011000002


N° SIREN
509067807


N° SIRET
50906780700032


##### Forme juridique

loi1901, Association déclarée

Culture


##### Objet

création de spectacles vivants, diffusion, production de spectacles vivants ; favoriser l'accès à l'art pour tous, notamment à la musique, par des animations collectives ou individuelles ; sensibiliser à des problématiques du monde actuel par des ateliers, des publications d'écrits ou des œuvres artistiques ;

##### Objet social 1

006100 - promotion de l'art et des artistes

##### Objet social 2

\- \- \-

##### Activités principale (APE)

90.01Z - Arts du spectacle vivant

##### Champ d'action

local

##### Adresse

Mairie

26220 Dieulefit

##### Adresse de gestion

Mairie

rue Justin Jouve

26220 Dieulefit

FRANCE

Téléphone


0695730954


Courriel


<EMAIL>'''
    
    # Créer une instance du scraper
    scraper = ScraperFinalReel()
    
    # Parser les données avec correction pour téléphone et email
    details = scraper.parse_contact_details(markdown_data, "W011000002")
    
    # Correction manuelle pour les champs non détectés
    if not details['telephone']:
        tel_match = re.search(r'Téléphone\n\n\n([^\n]+)', markdown_data)
        if tel_match:
            details['telephone'] = tel_match.group(1).strip()
    
    if not details['email']:
        email_match = re.search(r'Courriel\n\n\n([^\n]+)', markdown_data)
        if email_match:
            details['email'] = email_match.group(1).strip()
    
    print("=== DONNÉES EXTRAITES POUR W011000002 ===")
    for key, value in details.items():
        print(f"{key.upper()}: {value}")
    
    # Lire le fichier CSV existant
    csv_file = "coordonnees_10_associations.csv"
    associations = []
    
    with open(csv_file, 'r', encoding='utf-8') as file:
        reader = csv.DictReader(file)
        for row in reader:
            if row['rna'] == 'W011000002':
                # Remplacer par les vraies données
                row.update(details)
            associations.append(row)
    
    # Sauvegarder le fichier mis à jour
    with open(csv_file, 'w', newline='', encoding='utf-8') as file:
        if associations:
            fieldnames = associations[0].keys()
            writer = csv.DictWriter(file, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(associations)
    
    print(f"\nFichier {csv_file} mis à jour avec les vraies données de W011000002")
    
    return details

if __name__ == "__main__":
    update_association_w011000002()