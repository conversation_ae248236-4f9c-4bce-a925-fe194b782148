#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
INTERFACE TKINTER STANDARD - SCRAPER D'ASSOCIATIONS
Version avec tkinter standard - 100% compatible tous systèmes
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import os
import sys
import subprocess
import threading
import time
from datetime import datetime

# Fix encodage Windows
if sys.platform == "win32":
    import locale
    try:
        locale.setlocale(locale.LC_ALL, 'fr_FR.UTF-8')
    except:
        try:
            locale.setlocale(locale.LC_ALL, 'C.UTF-8')
        except:
            pass

# Fix PyInstaller
try:
    if sys.stdout is None:
        sys.stdout = open(os.devnull, "w", encoding='utf-8')
    if sys.stderr is None:
        sys.stderr = open(os.devnull, "w", encoding='utf-8')
except:
    pass

def resource_path(relative_path):
    """Get absolute path to resource"""
    try:
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(".")
    return os.path.join(base_path, relative_path)

class ScraperAppStandard:
    def __init__(self):
        # Variables
        self.output_file = "associations_coordonnees.xlsx"
        self.selected_departments = []
        self.scraping_active = False
        self.total_scraped = 0
        self.dept_checkboxes = {}
        
        # Fenêtre principale - STANDARD TKINTER
        self.root = tk.Tk()
        self.root.title("SCRAPER D'ASSOCIATIONS")
        
        # Style moderne
        self.setup_style()
        
        # Interface sécurisée
        self.setup_interface()
        self.load_departments()
        
        # Centrage sécurisé après création
        self.root.after(100, self.center_window)
        
    def setup_style(self):
        """Configuration du style"""
        style = ttk.Style()
        
        # Thème sombre si disponible
        try:
            style.theme_use('clam')
        except:
            pass
            
        # Configuration des couleurs
        self.root.configure(bg='#2b2b2b')
        
    def center_window(self):
        """Centre la fenêtre de manière ultra-sécurisée"""
        try:
            self.root.update_idletasks()
            
            # Dimensions de base
            width = max(800, self.root.winfo_reqwidth())
            height = max(600, self.root.winfo_reqheight())
            
            # Centre de l'écran
            screen_width = self.root.winfo_screenwidth()
            screen_height = self.root.winfo_screenheight()
            
            x = max(0, min((screen_width - width) // 2, screen_width - width))
            y = max(0, min((screen_height - height) // 2, screen_height - height))
            
            # Application ultra-sécurisée
            self.root.geometry(f"{width}x{height}+{x}+{y}")
            
        except:
            # Si ça échoue, on garde les dimensions par défaut
            pass
        
    def setup_interface(self):
        """Interface avec tkinter standard"""
        # Frame principal avec scroll
        canvas = tk.Canvas(self.root, bg='#2b2b2b', highlightthickness=0)
        scrollbar = ttk.Scrollbar(self.root, orient="vertical", command=canvas.yview)
        self.scrollable_frame = ttk.Frame(canvas)
        
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # === TITRE ===
        title_frame = ttk.Frame(self.scrollable_frame)
        title_frame.pack(fill="x", padx=20, pady=20)
        
        title_label = ttk.Label(
            title_frame,
            text="SCRAPER D'ASSOCIATIONS",
            font=("Arial", 20, "bold")
        )
        title_label.pack()
        
        # === MÉTRIQUES ===
        metrics_frame = ttk.LabelFrame(self.scrollable_frame, text="MÉTRIQUES", padding=10)
        metrics_frame.pack(fill="x", padx=20, pady=10)
        
        ttk.Label(metrics_frame, text="Total traité:", font=("Arial", 12)).pack()
        self.total_label = ttk.Label(
            metrics_frame,
            text="0",
            font=("Arial", 16, "bold"),
            foreground="#4CAF50"
        )
        self.total_label.pack()
        
        # === BOUTONS PRINCIPAUX ===
        buttons_frame = ttk.LabelFrame(self.scrollable_frame, text="CONTRÔLES", padding=10)
        buttons_frame.pack(fill="x", padx=20, pady=10)
        
        self.start_btn = ttk.Button(
            buttons_frame,
            text="🚀 DÉMARRER SCRAPING",
            command=self.start_scraping
        )
        self.start_btn.pack(fill="x", pady=5)
        
        self.stop_btn = ttk.Button(
            buttons_frame,
            text="🛑 ARRÊTER",
            command=self.stop_scraping,
            state="disabled"
        )
        self.stop_btn.pack(fill="x", pady=5)
        
        # === BOUTON FICHIER ===
        file_frame = ttk.LabelFrame(self.scrollable_frame, text="RÉSULTATS", padding=10)
        file_frame.pack(fill="x", padx=20, pady=10)
        
        self.open_file_btn = ttk.Button(
            file_frame,
            text="📊 OUVRIR FICHIER EXCEL",
            command=self.open_results_file
        )
        self.open_file_btn.pack(fill="x", pady=5)
        
        # === PARAMÈTRES ===
        params_frame = ttk.LabelFrame(self.scrollable_frame, text="PARAMÈTRES", padding=10)
        params_frame.pack(fill="x", padx=20, pady=10)
        
        # Limite
        ttk.Label(params_frame, text="Limite par département:").pack(anchor="w")
        self.limit_entry = ttk.Entry(params_frame)
        self.limit_entry.pack(fill="x", pady=5)
        self.limit_entry.insert(0, "Vide = illimité")
        
        # Processus
        ttk.Label(params_frame, text="Nombre de processus:").pack(anchor="w")
        self.process_var = tk.IntVar(value=4)
        self.process_scale = ttk.Scale(
            params_frame,
            from_=1, to=8,
            orient="horizontal",
            variable=self.process_var,
            command=self.update_process_label
        )
        self.process_scale.pack(fill="x", pady=5)
        
        self.process_label = ttk.Label(params_frame, text="4 processus")
        self.process_label.pack()
        
        # === DÉPARTEMENTS ===
        dept_frame = ttk.LabelFrame(self.scrollable_frame, text="SÉLECTION DÉPARTEMENTS", padding=10)
        dept_frame.pack(fill="x", padx=20, pady=10)
        
        # Boutons sélection
        btn_frame = ttk.Frame(dept_frame)
        btn_frame.pack(fill="x", pady=5)
        
        ttk.Button(btn_frame, text="✅ Tous", command=self.select_all).pack(side="left", padx=5)
        ttk.Button(btn_frame, text="❌ Aucun", command=self.deselect_all).pack(side="left", padx=5)
        ttk.Button(btn_frame, text="🔄 Inverser", command=self.invert_all).pack(side="left", padx=5)
        
        # Zone départements avec frame interne
        self.dept_canvas = tk.Canvas(dept_frame, height=200, bg='white')
        dept_scrollbar = ttk.Scrollbar(dept_frame, orient="vertical", command=self.dept_canvas.yview)
        self.dept_inner_frame = ttk.Frame(self.dept_canvas)
        
        self.dept_inner_frame.bind(
            "<Configure>",
            lambda e: self.dept_canvas.configure(scrollregion=self.dept_canvas.bbox("all"))
        )
        
        self.dept_canvas.create_window((0, 0), window=self.dept_inner_frame, anchor="nw")
        self.dept_canvas.configure(yscrollcommand=dept_scrollbar.set)
        
        self.dept_canvas.pack(side="left", fill="both", expand=True)
        dept_scrollbar.pack(side="right", fill="y")
        
        # Compteur
        self.dept_count = ttk.Label(dept_frame, text="0 départements sélectionnés", font=("Arial", 10, "bold"))
        self.dept_count.pack(pady=5)
        
        # === LOGS ===
        logs_frame = ttk.LabelFrame(self.scrollable_frame, text="LOGS DE SCRAPING", padding=10)
        logs_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        self.log_text = scrolledtext.ScrolledText(
            logs_frame,
            height=15,
            font=("Consolas", 9),
            wrap=tk.WORD
        )
        self.log_text.pack(fill="both", expand=True)
        
        # Message initial
        self.log("Application prête ! Sélectionnez vos départements et cliquez DÉMARRER.")
        
    def load_departments(self):
        """Chargement des départements"""
        try:
            dept_dir = resource_path("rna_waldec_20250701")
            
            if not os.path.exists(dept_dir):
                self.log("❌ Dossier des données non trouvé!")
                return
                
            files = [f for f in os.listdir(dept_dir) if f.endswith('.csv') and '_dpt_' in f]
            files.sort()
            
            if not files:
                self.log("❌ Aucun fichier de données trouvé!")
                return
            
            # Créer checkboxes - 4 colonnes
            for i, file in enumerate(files):
                dept_code = file.split('_dpt_')[1].split('.')[0]
                
                var = tk.BooleanVar()
                cb = ttk.Checkbutton(
                    self.dept_inner_frame,
                    text=f"Dept {dept_code}",
                    variable=var,
                    command=self.update_count
                )
                
                # 4 colonnes
                row = i // 4
                col = i % 4
                cb.grid(row=row, column=col, padx=5, pady=2, sticky="w")
                
                # Sélectionner les 10 premiers
                if i < 10:
                    var.set(True)
                
                self.dept_checkboxes[dept_code] = var
            
            self.update_count()
            self.log(f"✅ {len(files)} départements chargés")
            
        except Exception as e:
            self.log(f"❌ Erreur: {e}")
    
    def update_count(self):
        """Met à jour le compteur"""
        selected = sum(1 for var in self.dept_checkboxes.values() if var.get())
        self.dept_count.config(text=f"{selected} départements sélectionnés")
    
    def update_process_label(self, value):
        """Met à jour le label processus"""
        self.process_label.config(text=f"{int(float(value))} processus")
    
    def select_all(self):
        """Sélectionner tous"""
        for var in self.dept_checkboxes.values():
            var.set(True)
        self.update_count()
    
    def deselect_all(self):
        """Désélectionner tous"""
        for var in self.dept_checkboxes.values():
            var.set(False)
        self.update_count()
    
    def invert_all(self):
        """Inverser la sélection"""
        for var in self.dept_checkboxes.values():
            var.set(not var.get())
        self.update_count()
    
    def open_results_file(self):
        """Ouvrir directement le fichier de résultats"""
        try:
            # Chercher le fichier Excel le plus récent
            excel_files = [f for f in os.listdir('.') if f.endswith('.xlsx') and 'associations' in f.lower()]
            csv_files = [f for f in os.listdir('.') if f.endswith('.csv') and 'associations' in f.lower()]
            
            file_to_open = None
            
            if excel_files:
                excel_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
                file_to_open = excel_files[0]
            elif csv_files:
                csv_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
                file_to_open = csv_files[0]
            elif os.path.exists(self.output_file):
                file_to_open = self.output_file
            else:
                messagebox.showinfo("Info", "❌ Aucun fichier trouvé!\nLancez d'abord un scraping.")
                return
            
            # Ouvrir le fichier
            self.log(f"📂 Ouverture: {file_to_open}")
            
            if sys.platform == "win32":
                os.startfile(file_to_open)
            elif sys.platform == "darwin":
                subprocess.call(["open", file_to_open])
            else:
                subprocess.call(["xdg-open", file_to_open])
                
            self.log("✅ Fichier ouvert dans Excel")
            
        except Exception as e:
            self.log(f"❌ Erreur: {e}")
            messagebox.showerror("Erreur", f"Impossible d'ouvrir:\n{e}")
    
    def start_scraping(self):
        """Démarrer le scraping"""
        selected = [code for code, var in self.dept_checkboxes.items() if var.get()]
        
        if not selected:
            messagebox.showwarning("Attention", "Sélectionnez au moins un département!")
            return
        
        self.selected_departments = selected
        self.scraping_active = True
        
        # Interface
        self.start_btn.config(state="disabled", text="🔄 EN COURS...")
        self.stop_btn.config(state="normal")
        
        self.log(f"🚀 Démarrage pour {len(selected)} départements")
        
        # Lancer le vrai scraping
        threading.Thread(target=self.real_scraping, daemon=True).start()
    
    def real_scraping(self):
        """VRAI scraping avec le scraper réel"""
        try:
            # Import du scraper réel
            import scraper as sc
            
            # Paramètres de scraping
            limit_text = self.limit_entry.get().strip()
            limit_per_dept = -1
            if limit_text and limit_text.isdigit():
                limit_per_dept = int(limit_text)
            
            params = {
                'driver_path': './chromedriver.exe',
                'departments': self.selected_departments,
                'max_workers': self.process_var.get(),
                'limit_per_dept': limit_per_dept
            }
            
            self.log(f"🔧 Paramètres: {len(self.selected_departments)} depts, {params['max_workers']} processus")
            if limit_per_dept > 0:
                self.log(f"🎯 Limite: {limit_per_dept} par département")
            
            # Lancer le scraping réel
            sc.run_scraper(params)
            
            if self.scraping_active:
                self.log(f"🎉 Scraping terminé ! Vérifiez le fichier Excel.")
                self.root.after(0, self.reset_interface)
            
        except Exception as e:
            self.log(f"❌ Erreur scraping: {e}")
            self.root.after(0, self.reset_interface)
    
    def stop_scraping(self):
        """Arrêter le scraping"""
        self.scraping_active = False
        self.log("🛑 Arrêt demandé...")
        self.reset_interface()
    
    def reset_interface(self):
        """Remettre l'interface normale"""
        self.start_btn.config(state="normal", text="🚀 DÉMARRER SCRAPING")
        self.stop_btn.config(state="disabled")
        self.scraping_active = False
    
    def log(self, message):
        """Ajouter un message aux logs"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        full_message = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, full_message)
        self.log_text.see(tk.END)
        
        # Limiter les lignes
        lines = self.log_text.get("1.0", tk.END).count('\n')
        if lines > 200:
            self.log_text.delete("1.0", "50.0")
    
    def run(self):
        """Lancer l'application"""
        self.root.mainloop()

def main():
    """Fonction principale"""
    try:
        app = ScraperAppStandard()
        app.run()
    except Exception as e:
        print(f"Erreur: {e}")

if __name__ == "__main__":
    main() 