#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Diagnostic complet du problème CSV
"""

import csv
import os

def diagnostic_csv():
    """Diagnostic complet du fichier CSV"""
    print("🔍 === DIAGNOSTIC COMPLET DU FICHIER CSV ===")
    
    csv_path = "data/rna_waldec_20250701/rna_waldec_20250701_dpt_975.csv"
    
    # 1. Vérifier l'existence
    exists = os.path.exists(csv_path)
    print(f"📁 Fichier existe: {exists}")
    print(f"📁 Chemin complet: {os.path.abspath(csv_path)}")
    
    if not exists:
        print("❌ Fichier non trouvé - arrêt du diagnostic")
        return
    
    # 2. Vérifier la taille
    size = os.path.getsize(csv_path)
    print(f"📊 Taille du fichier: {size:,} bytes")
    
    # 3. Lire les premières lignes brutes
    print("\n📋 === CONTENU BRUT ===")
    with open(csv_path, 'r', encoding='utf-8') as f:
        for i in range(3):
            line = f.readline()
            print(f"Ligne {i+1}: {repr(line)}")
    
    # 4. Test avec csv.DictReader
    print("\n📋 === TEST CSV.DICTREADER ===")
    try:
        with open(csv_path, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f, delimiter=';')
            print(f"Headers: {reader.fieldnames}")
            
            rnas_found = []
            for i, row in enumerate(reader):
                if i >= 5:
                    break
                
                print(f"\nLigne {i+1}:")
                print(f"  Clés disponibles: {list(row.keys())}")
                
                # Test différentes méthodes d'extraction
                id_raw = row.get('id', '')
                id_stripped = row.get('id', '').strip()
                id_clean = row.get('id', '').strip().strip('"')
                
                print(f"  ID brut: {repr(id_raw)}")
                print(f"  ID après strip(): {repr(id_stripped)}")
                print(f"  ID après strip('\"'): {repr(id_clean)}")
                
                if id_clean:
                    rnas_found.append(id_clean)
                    
            print(f"\n✅ RNA trouvés: {len(rnas_found)}")
            for rna in rnas_found:
                print(f"  - {rna}")
                
    except Exception as e:
        print(f"❌ Erreur lors de la lecture CSV: {e}")
        import traceback
        traceback.print_exc()
    
    # 5. Test encodages différents
    print("\n📋 === TEST ENCODAGES DIFFÉRENTS ===")
    encodings = ['utf-8', 'latin-1', 'cp1252', 'utf-8-sig']
    
    for encoding in encodings:
        try:
            with open(csv_path, 'r', encoding=encoding) as f:
                first_line = f.readline()
                print(f"Encodage {encoding}: {repr(first_line[:50])}")
        except Exception as e:
            print(f"Encodage {encoding}: ERREUR - {e}")

if __name__ == "__main__":
    diagnostic_csv()